import os
import glob
import shutil
import time
import json
from typing import List, Dict, Any, Optional, Tuple
from langchain_openai import AzureOpenAIEmbeddings
from langchain.text_splitter import RecursiveCharacterTextSplitter
from langchain_community.vectorstores import FAISS
from langchain_community.document_loaders import TextLoader
from langchain_core.documents import Document
from utils.api_key import get_azure_openai_config
from utils.document_converter import DocumentConverter
import logging

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class HierarchicalVectorDatabase:
    """
    Hierarchical Vector Database that maintains separate vector stores for each category/subcategory
    in the master database, enabling targeted RAG retrieval based on template selection.
    """

    def __init__(self, base_path: str = "master_db"):
        """
        Initialize the hierarchical vector database

        Args:
            base_path: Base path to the master database directory
        """
        self.base_path = base_path
        self.db_base_path = "database/hierarchical_master_db"
        self.category_mapping_file = os.path.join(self.db_base_path, "category_mapping.json")

        # Initialize embeddings
        azure_config = get_azure_openai_config()
        embedding_deployment = azure_config.get('embedding_deployment', 'text-embedding-3-small')

        self.embeddings = AzureOpenAIEmbeddings(
            azure_deployment=embedding_deployment,
            api_key=azure_config['api_key'],
            azure_endpoint=azure_config['endpoint'],
            api_version=azure_config['api_version'],
            chunk_size=1
        )

        self.text_splitter = RecursiveCharacterTextSplitter(
            chunk_size=500,
            chunk_overlap=100,
            separators=["\n\n", "\n", ".", " ", ""]
        )

        # Rate limiting settings
        self.batch_size = 10
        self.delay_between_batches = 2
        self.max_retries = 3
        self.retry_delay = 60

        # Category structure cache
        self.category_structure = {}
        self.vector_stores = {}

        # Ensure base directory exists
        os.makedirs(self.db_base_path, exist_ok=True)

    def discover_category_structure(self) -> Dict[str, Any]:
        """
        Discover the hierarchical structure of the master database

        Returns:
            Dictionary representing the category structure
        """
        logger.info(f"Discovering category structure in {self.base_path}")
        structure = {}

        if not os.path.exists(self.base_path):
            logger.warning(f"Master database path {self.base_path} does not exist")
            return structure

        # Walk through the directory structure
        for root, dirs, files in os.walk(self.base_path):
            # Get relative path from base
            rel_path = os.path.relpath(root, self.base_path)

            if rel_path == ".":
                # Root level categories
                for dir_name in dirs:
                    structure[dir_name] = {
                        "path": os.path.join(self.base_path, dir_name),
                        "subcategories": {},
                        "files": []
                    }
            else:
                # Handle subcategories
                path_parts = rel_path.split(os.sep)
                current_level = structure

                # Navigate to the correct level in the structure
                for part in path_parts[:-1]:
                    if part in current_level:
                        current_level = current_level[part]["subcategories"]

                # Add current directory
                if path_parts[-1] not in current_level:
                    current_level[path_parts[-1]] = {
                        "path": root,
                        "subcategories": {},
                        "files": []
                    }

                # Add subdirectories
                for dir_name in dirs:
                    current_level[path_parts[-1]]["subcategories"][dir_name] = {
                        "path": os.path.join(root, dir_name),
                        "subcategories": {},
                        "files": []
                    }

            # Add files to the current level
            supported_extensions = ['.txt', '.pdf', '.docx', '.doc']
            for file_name in files:
                if any(file_name.lower().endswith(ext) for ext in supported_extensions):
                    file_path = os.path.join(root, file_name)

                    # Find the correct place to add this file
                    if rel_path == ".":
                        # Files in root (shouldn't happen in a well-structured DB)
                        continue
                    else:
                        path_parts = rel_path.split(os.sep)
                        current_level = structure

                        # Navigate to the correct level
                        for part in path_parts:
                            if part in current_level:
                                current_level = current_level[part]
                            elif "subcategories" in current_level and part in current_level["subcategories"]:
                                current_level = current_level["subcategories"][part]

                        if "files" in current_level:
                            current_level["files"].append({
                                "name": file_name,
                                "path": file_path,
                                "extension": os.path.splitext(file_name)[1].lower()
                            })

        self.category_structure = structure
        self._save_category_mapping()

        logger.info(f"Discovered {len(structure)} main categories")
        for category, info in structure.items():
            subcategory_count = len(info["subcategories"])
            file_count = len(info["files"])
            logger.info(f"  {category}: {subcategory_count} subcategories, {file_count} files")

        return structure

    def _save_category_mapping(self):
        """Save the category structure to a JSON file"""
        try:
            with open(self.category_mapping_file, 'w') as f:
                json.dump(self.category_structure, f, indent=2)
            logger.info(f"Category mapping saved to {self.category_mapping_file}")
        except Exception as e:
            logger.error(f"Failed to save category mapping: {e}")

    def _load_category_mapping(self) -> bool:
        """Load the category structure from JSON file"""
        try:
            if os.path.exists(self.category_mapping_file):
                with open(self.category_mapping_file, 'r') as f:
                    self.category_structure = json.load(f)
                logger.info(f"Category mapping loaded from {self.category_mapping_file}")
                return True
            return False
        except Exception as e:
            logger.error(f"Failed to load category mapping: {e}")
            return False

    def vectorize_all_categories(self):
        """
        Vectorize all categories and subcategories in the master database
        """
        logger.info("Starting hierarchical vectorization of master database")

        # Discover or load category structure
        if not self._load_category_mapping():
            self.discover_category_structure()

        # Vectorize each category
        for category_name, category_info in self.category_structure.items():
            self._vectorize_category(category_name, category_info)

        logger.info("Hierarchical vectorization completed")

    def _vectorize_category(self, category_name: str, category_info: Dict[str, Any], parent_path: str = ""):
        """
        Vectorize a specific category and its subcategories

        Args:
            category_name: Name of the category
            category_info: Category information dictionary
            parent_path: Path of parent category (for subcategories)
        """
        # Create category path for vector store
        if parent_path:
            full_category_path = f"{parent_path}/{category_name}"
        else:
            full_category_path = category_name

        logger.info(f"Vectorizing category: {full_category_path}")

        # Collect all documents for this category
        documents = []

        # Add files from this category
        for file_info in category_info.get("files", []):
            try:
                doc = self._load_document(file_info["path"], full_category_path)
                if doc:
                    documents.append(doc)
            except Exception as e:
                logger.error(f"Error loading {file_info['path']}: {e}")

        # Recursively add files from subcategories
        for subcategory_name, subcategory_info in category_info.get("subcategories", {}).items():
            subcategory_docs = self._collect_documents_recursive(
                subcategory_name, subcategory_info, full_category_path
            )
            documents.extend(subcategory_docs)

        # Create vector store for this category if we have documents
        if documents:
            try:
                # Split documents into chunks
                texts = self.text_splitter.split_documents(documents)
                logger.info(f"Created {len(texts)} chunks for category {full_category_path}")

                # Create vector store with rate limiting
                vector_store = self._create_embeddings_with_retry(texts)

                # Save the vector store
                category_db_path = os.path.join(self.db_base_path, full_category_path.replace("/", "_"))
                os.makedirs(category_db_path, exist_ok=True)
                vector_store.save_local(category_db_path)

                # Cache the vector store
                self.vector_stores[full_category_path] = vector_store

                logger.info(f"✓ Vectorized category {full_category_path} with {len(documents)} documents")

            except Exception as e:
                logger.error(f"Failed to vectorize category {full_category_path}: {e}")
        else:
            logger.warning(f"No documents found for category {full_category_path}")

    def _collect_documents_recursive(self, category_name: str, category_info: Dict[str, Any], parent_path: str) -> List[Document]:
        """
        Recursively collect documents from a category and its subcategories

        Args:
            category_name: Name of the category
            category_info: Category information dictionary
            parent_path: Path of parent category

        Returns:
            List of documents
        """
        documents = []
        full_path = f"{parent_path}/{category_name}"

        # Add files from this category
        for file_info in category_info.get("files", []):
            try:
                doc = self._load_document(file_info["path"], full_path)
                if doc:
                    documents.append(doc)
            except Exception as e:
                logger.error(f"Error loading {file_info['path']}: {e}")

        # Recursively collect from subcategories
        for subcategory_name, subcategory_info in category_info.get("subcategories", {}).items():
            subcategory_docs = self._collect_documents_recursive(
                subcategory_name, subcategory_info, full_path
            )
            documents.extend(subcategory_docs)

        return documents

    def _load_document(self, file_path: str, category_path: str) -> Optional[Document]:
        """
        Load a document from file with proper metadata

        Args:
            file_path: Path to the file
            category_path: Category path for metadata

        Returns:
            Document object or None if loading fails
        """
        try:
            file_ext = os.path.splitext(file_path)[1].lower()

            if file_ext == '.txt':
                loader = TextLoader(file_path)
                docs = loader.load()
                if docs:
                    doc = docs[0]
                    doc.metadata.update({
                        "category": category_path,
                        "filename": os.path.basename(file_path),
                        "file_type": file_ext
                    })
                    return doc

            elif file_ext in ['.pdf', '.docx', '.doc']:
                text = DocumentConverter.extract_text_from_file(file_path)
                if text.strip():
                    return Document(
                        page_content=text,
                        metadata={
                            "source": file_path,
                            "category": category_path,
                            "filename": os.path.basename(file_path),
                            "file_type": file_ext
                        }
                    )

        except Exception as e:
            logger.error(f"Error loading document {file_path}: {e}")

        return None

    def get_category_for_template(self, template_path: str) -> Optional[str]:
        """
        Determine which category a template belongs to based on its path

        Args:
            template_path: Path to the template file

        Returns:
            Category path or None if not found
        """
        # Extract category from template path
        # Assuming templates are organized like: templates/Category/Subcategory/template.pdf
        if "templates" in template_path:
            path_parts = template_path.split(os.sep)
            template_idx = path_parts.index("templates")

            if len(path_parts) > template_idx + 1:
                category = path_parts[template_idx + 1]

                # Check if there's a subcategory
                if len(path_parts) > template_idx + 2:
                    subcategory = path_parts[template_idx + 2]
                    # Check if this is actually a file (has extension)
                    if not os.path.splitext(subcategory)[1]:
                        return f"{category}/{subcategory}"

                return category

        return None

    def get_relevant_vector_store(self, template_path: str) -> Optional[FAISS]:
        """
        Get the relevant vector store for a given template

        Args:
            template_path: Path to the template file

        Returns:
            FAISS vector store or None if not found
        """
        category = self.get_category_for_template(template_path)
        if not category:
            logger.warning(f"Could not determine category for template: {template_path}")
            return None

        # Try to load the vector store for this category
        return self.load_category_vector_store(category)

    def load_category_vector_store(self, category_path: str) -> Optional[FAISS]:
        """
        Load vector store for a specific category

        Args:
            category_path: Category path (e.g., "Notice/Check Bounce")

        Returns:
            FAISS vector store or None if not found
        """
        # Check if already loaded
        if category_path in self.vector_stores:
            return self.vector_stores[category_path]

        # Try to load from disk
        category_db_path = os.path.join(self.db_base_path, category_path.replace("/", "_"))

        try:
            if os.path.exists(category_db_path):
                vector_store = FAISS.load_local(
                    category_db_path,
                    self.embeddings,
                    allow_dangerous_deserialization=True
                )
                self.vector_stores[category_path] = vector_store
                logger.info(f"Loaded vector store for category: {category_path}")
                return vector_store
            else:
                logger.warning(f"Vector store not found for category: {category_path}")
                return None

        except Exception as e:
            logger.error(f"Error loading vector store for category {category_path}: {e}")
            return None

    def search_category(self, query: str, category_path: str, k: int = 5) -> List[Document]:
        """
        Search within a specific category

        Args:
            query: Search query
            category_path: Category path to search in
            k: Number of results to return

        Returns:
            List of relevant documents
        """
        vector_store = self.load_category_vector_store(category_path)
        if vector_store:
            try:
                results = vector_store.similarity_search(query, k=k)
                logger.info(f"Found {len(results)} results for query in category {category_path}")
                return results
            except Exception as e:
                logger.error(f"Error searching category {category_path}: {e}")

        return []

    def _create_embeddings_with_retry(self, texts: List[Document]) -> FAISS:
        """Create embeddings with rate limiting and retry logic"""

        # If we have a small number of texts, try to process them all at once first
        if len(texts) <= 5:
            try:
                logger.info(f"Attempting to process {len(texts)} documents at once...")
                return FAISS.from_documents(texts, self.embeddings)
            except Exception as e:
                if "429" in str(e) or "rate limit" in str(e).lower():
                    logger.warning("Rate limit hit, switching to batch processing...")
                else:
                    raise e

        # Batch processing for rate limiting
        logger.info(f"Processing {len(texts)} documents in batches of {self.batch_size}")

        vector_store = None
        total_batches = (len(texts) + self.batch_size - 1) // self.batch_size

        for i in range(0, len(texts), self.batch_size):
            batch = texts[i:i + self.batch_size]
            batch_num = (i // self.batch_size) + 1

            logger.info(f"Processing batch {batch_num}/{total_batches} ({len(batch)} documents)")

            retry_count = 0
            while retry_count < self.max_retries:
                try:
                    if vector_store is None:
                        # Create initial vector store
                        vector_store = FAISS.from_documents(batch, self.embeddings)
                    else:
                        # Add to existing vector store
                        batch_store = FAISS.from_documents(batch, self.embeddings)
                        vector_store.merge_from(batch_store)

                    logger.info(f"✓ Batch {batch_num} processed successfully")
                    break

                except Exception as e:
                    if "429" in str(e) or "rate limit" in str(e).lower():
                        retry_count += 1
                        if retry_count < self.max_retries:
                            logger.warning(f"Rate limit hit, retrying batch {batch_num} in {self.retry_delay} seconds... (attempt {retry_count}/{self.max_retries})")
                            time.sleep(self.retry_delay)
                        else:
                            logger.error(f"Failed to process batch {batch_num} after {self.max_retries} retries")
                            raise
                    else:
                        logger.error(f"Error processing batch {batch_num}: {e}")
                        raise

            # Add delay between batches to avoid rate limiting
            if i + self.batch_size < len(texts):
                logger.info(f"Waiting {self.delay_between_batches} seconds before next batch...")
                time.sleep(self.delay_between_batches)

        return vector_store

class VectorDatabase:
    def __init__(self, collection_name: str, category: Optional[str] = None):
        """
        Initialize the vector database with rate limiting

        Args:
            collection_name: Name of the collection (used for storing the index)
            category: Optional category for master_db (e.g., "agreements", "contracts")
        """
        self.collection_name = collection_name
        self.category = category
        
        # Rate limiting settings for Azure OpenAI
        self.batch_size = 10  # Process 10 documents at a time
        self.delay_between_batches = 2  # Wait 2 seconds between batches
        self.max_retries = 3  # Retry failed requests up to 3 times
        self.retry_delay = 60  # Wait 60 seconds before retrying on rate limit

        # Use Azure OpenAI embeddings
        azure_config = get_azure_openai_config()
        embedding_deployment = azure_config.get('embedding_deployment', 'text-embedding-3-small')
        
        try:
            self.embeddings = AzureOpenAIEmbeddings(
                azure_deployment=embedding_deployment,
                api_key=azure_config['api_key'],
                azure_endpoint=azure_config['endpoint'],
                api_version=azure_config['api_version'],
                chunk_size=1  # Process one chunk at a time to avoid rate limits
            )
        except Exception as e:
            logger.error(f"Failed to initialize embeddings with deployment '{embedding_deployment}'")
            logger.error(f"Error: {e}")
            raise
            
        self.vector_store = None
        self.text_splitter = RecursiveCharacterTextSplitter(
            chunk_size=500,  # Smaller chunks to reduce API calls
            chunk_overlap=100,
            separators=["\n\n", "\n", ".", " ", ""]
        )

        # Set the database path based on collection and category
        if category and collection_name == "master_db":
            self.db_path = f"database/{collection_name}/{category}"
        else:
            self.db_path = f"database/{collection_name}"

    @staticmethod
    def reset_database():
        """Delete and recreate the database directory"""
        if os.path.exists("database"):
            shutil.rmtree("database")
        os.makedirs("database", exist_ok=True)
        os.makedirs("database/master_db", exist_ok=True)
        os.makedirs("database/client_files", exist_ok=True)
        print("Database directories reset successfully")

    def index_directory(self, directory_path: str) -> None:
        """
        Index all files in a directory with rate limiting

        Args:
            directory_path: Path to the directory containing files
        """
        logger.info(f"Starting to index directory: {directory_path}")
        documents = []

        # For master_db with category, only index files from that category
        if self.collection_name == "master_db" and self.category:
            category_path = os.path.join(directory_path, self.category)
            if os.path.exists(category_path):
                directory_path = category_path
                logger.info(f"Indexing category: {self.category}")
            else:
                logger.warning(f"Category path {category_path} does not exist")
                return

        # Get all supported files in the directory and subdirectories
        supported_extensions = ['*.txt', '*.pdf', '*.docx', '*.doc']
        file_count = 0
        
        for extension in supported_extensions:
            files = glob.glob(f"{directory_path}/**/{extension}", recursive=True)
            logger.info(f"Found {len(files)} {extension} files")
            
            for file_path in files:
                try:
                    file_ext = os.path.splitext(file_path)[1].lower()
                    
                    if file_ext == '.txt':
                        loader = TextLoader(file_path)
                        file_docs = loader.load()
                        documents.extend(file_docs)
                        file_count += 1
                        
                    elif file_ext in ['.pdf', '.docx', '.doc']:
                        # Use DocumentConverter to extract text from PDFs and DOCX files
                        try:
                            text = DocumentConverter.extract_text_from_file(file_path)
                            if text.strip():  # Only add if there's actual content
                                doc = Document(
                                    page_content=text,
                                    metadata={
                                        "source": file_path,
                                        "filename": os.path.basename(file_path),
                                        "file_type": file_ext
                                    }
                                )
                                documents.append(doc)
                                file_count += 1
                                logger.info(f"✓ Processed: {os.path.basename(file_path)} ({len(text)} chars)")
                        except Exception as e:
                            logger.error(f"Error extracting text from {file_path}: {e}")
                            continue
                            
                except Exception as e:
                    logger.error(f"Error loading {file_path}: {e}")

        if not documents:
            logger.warning(f"No documents found in {directory_path}")
            return

        logger.info(f"Successfully loaded {file_count} files, total {len(documents)} documents")

        # Split documents into chunks
        logger.info("Splitting documents into chunks...")
        texts = self.text_splitter.split_documents(documents)
        logger.info(f"Created {len(texts)} text chunks")

        # Create vector store with rate limiting
        logger.info("Creating embeddings with rate limiting...")
        try:
            self.vector_store = self._create_embeddings_with_retry(texts)
            logger.info("✓ Vector store created successfully")
        except Exception as e:
            logger.error(f"Failed to create vector store: {e}")
            raise

        # Save the index
        logger.info("Saving index to disk...")
        self.save_index()
        logger.info("✓ Index saved successfully")

    def save_index(self) -> None:
        """Save the FAISS index to disk"""
        if self.vector_store:
            # Ensure the directory exists
            os.makedirs(self.db_path, exist_ok=True)
            self.vector_store.save_local(self.db_path)

    def load_index(self) -> bool:
        """
        Load the FAISS index from disk

        Returns:
            bool: True if index was loaded successfully, False otherwise
        """
        try:
            if os.path.exists(self.db_path):
                self.vector_store = FAISS.load_local(
                    self.db_path,
                    self.embeddings,
                    allow_dangerous_deserialization=True
                )
                return True
            else:
                print(f"Index path {self.db_path} does not exist")
                return False
        except Exception as e:
            logger.error(f"Error loading index: {e}")
            return False

    def index_files(self, file_paths: List[str]) -> None:
        """
        Index files of various formats (txt, docx, pdf) with rate limiting

        Args:
            file_paths: List of paths to files
        """
        logger.info(f"Starting to index {len(file_paths)} files")
        documents = []

        # Load each file
        for file_path in file_paths:
            try:
                # Check file extension
                _, ext = os.path.splitext(file_path)
                ext = ext.lower()

                if ext == '.txt':
                    # Use TextLoader for txt files
                    loader = TextLoader(file_path)
                    file_docs = loader.load()
                    documents.extend(file_docs)
                    logger.info(f"✓ Loaded: {os.path.basename(file_path)}")
                elif ext in ['.docx', '.pdf']:
                    # Extract text from docx or pdf files
                    text = DocumentConverter.extract_text_from_file(file_path)
                    if text.strip():
                        # Create a Document object
                        doc = Document(
                            page_content=text,
                            metadata={
                                "source": file_path,
                                "filename": os.path.basename(file_path),
                                "file_type": ext
                            }
                        )
                        documents.append(doc)
                        logger.info(f"✓ Processed: {os.path.basename(file_path)} ({len(text)} chars)")
                else:
                    logger.warning(f"Unsupported file format: {ext}")
                    continue
            except Exception as e:
                logger.error(f"Error loading {file_path}: {e}")

        if not documents:
            logger.warning("No documents were loaded.")
            return

        logger.info(f"Successfully loaded {len(documents)} documents")

        # Split documents into chunks
        logger.info("Splitting documents into chunks...")
        texts = self.text_splitter.split_documents(documents)
        logger.info(f"Created {len(texts)} text chunks")

        # Create vector store with rate limiting
        logger.info("Creating embeddings with rate limiting...")
        try:
            self.vector_store = self._create_embeddings_with_retry(texts)
            logger.info("✓ Vector store created successfully")
        except Exception as e:
            logger.error(f"Failed to create vector store: {e}")
            raise

        # Save the index
        logger.info("Saving index to disk...")
        self.save_index()
        logger.info("✓ Index saved successfully")

    def search(self, query: str, k: int = 5) -> List[Dict[str, Any]]:
        """
        Search the vector database

        Args:
            query: Search query
            k: Number of results to return

        Returns:
            List of documents with their scores
        """
        if not self.vector_store:
            if not self.load_index():
                return []

        results = self.vector_store.similarity_search_with_score(query, k=k)

        return [
            {
                "content": doc.page_content,
                "metadata": doc.metadata,
                "score": score
            }
            for doc, score in results
        ]

    def _create_embeddings_with_retry(self, texts: List[Document]) -> FAISS:
        """Create embeddings with rate limiting and retry logic"""
        
        # If we have a small number of texts, try to process them all at once first
        if len(texts) <= 5:
            try:
                logger.info(f"Attempting to process {len(texts)} documents at once...")
                return FAISS.from_documents(texts, self.embeddings)
            except Exception as e:
                if "429" in str(e) or "rate limit" in str(e).lower():
                    logger.warning("Rate limit hit, switching to batch processing...")
                else:
                    raise e
        
        # Batch processing for rate limiting
        logger.info(f"Processing {len(texts)} documents in batches of {self.batch_size}")
        
        vector_store = None
        total_batches = (len(texts) + self.batch_size - 1) // self.batch_size
        
        for i in range(0, len(texts), self.batch_size):
            batch = texts[i:i + self.batch_size]
            batch_num = (i // self.batch_size) + 1
            
            logger.info(f"Processing batch {batch_num}/{total_batches} ({len(batch)} documents)")
            
            retry_count = 0
            while retry_count < self.max_retries:
                try:
                    if vector_store is None:
                        # Create the initial vector store
                        vector_store = FAISS.from_documents(batch, self.embeddings)
                    else:
                        # Add to existing vector store
                        new_vs = FAISS.from_documents(batch, self.embeddings)
                        vector_store.merge_from(new_vs)
                    
                    logger.info(f"✓ Batch {batch_num} processed successfully")
                    break
                    
                except Exception as e:
                    if "429" in str(e) or "rate limit" in str(e).lower():
                        retry_count += 1
                        logger.warning(f"Rate limit hit for batch {batch_num}, retry {retry_count}/{self.max_retries}")
                        
                        if retry_count < self.max_retries:
                            logger.info(f"Waiting {self.retry_delay} seconds before retrying...")
                            time.sleep(self.retry_delay)
                        else:
                            logger.error(f"Max retries exceeded for batch {batch_num}")
                            raise e
                    else:
                        logger.error(f"Non-rate-limit error in batch {batch_num}: {e}")
                        raise e
            
            # Add delay between batches to avoid rate limits
            if i + self.batch_size < len(texts):
                logger.info(f"Waiting {self.delay_between_batches} seconds before next batch...")
                time.sleep(self.delay_between_batches)
        
        return vector_store
