"""
Master Database Manager for Hierarchical Vectorization

This module provides a high-level interface for managing the hierarchical
vectorization of the master database, enabling targeted RAG retrieval
based on template categories.
"""

import os
import logging
from typing import List, Dict, Any, Optional
from database.vector_db import HierarchicalVectorDatabase
from langchain_core.documents import Document

logger = logging.getLogger(__name__)


class MasterDatabaseManager:
    """
    High-level manager for the hierarchical master database vectorization system
    """
    
    def __init__(self, master_db_path: str = "master_db"):
        """
        Initialize the master database manager
        
        Args:
            master_db_path: Path to the master database directory
        """
        self.master_db_path = master_db_path
        self.hierarchical_db = HierarchicalVectorDatabase(master_db_path)
        self._initialized = False
    
    def initialize(self, force_rebuild: bool = False):
        """
        Initialize the hierarchical vector database
        
        Args:
            force_rebuild: Whether to force rebuild all vector stores
        """
        logger.info("Initializing Master Database Manager")
        
        if force_rebuild:
            logger.info("Force rebuild requested - clearing existing vector stores")
            self._clear_vector_stores()
        
        # Check if we need to build the vector stores
        if not self._check_vector_stores_exist() or force_rebuild:
            logger.info("Building hierarchical vector stores...")
            self.hierarchical_db.vectorize_all_categories()
        else:
            logger.info("Vector stores already exist, loading category structure...")
            self.hierarchical_db._load_category_mapping()
        
        self._initialized = True
        logger.info("Master Database Manager initialized successfully")
    
    def _check_vector_stores_exist(self) -> bool:
        """Check if vector stores have been created"""
        return os.path.exists(self.hierarchical_db.category_mapping_file)
    
    def _clear_vector_stores(self):
        """Clear all existing vector stores"""
        import shutil
        if os.path.exists(self.hierarchical_db.db_base_path):
            shutil.rmtree(self.hierarchical_db.db_base_path)
        os.makedirs(self.hierarchical_db.db_base_path, exist_ok=True)
    
    def get_category_structure(self) -> Dict[str, Any]:
        """
        Get the hierarchical category structure
        
        Returns:
            Dictionary representing the category structure
        """
        if not self._initialized:
            self.initialize()
        
        return self.hierarchical_db.category_structure
    
    def get_relevant_context(self, template_path: str, query: str, k: int = 5) -> List[Document]:
        """
        Get relevant context from the master database for a given template and query
        
        Args:
            template_path: Path to the template file
            query: Query string for context retrieval
            k: Number of documents to retrieve
            
        Returns:
            List of relevant documents
        """
        if not self._initialized:
            self.initialize()
        
        # Determine the category for this template
        category = self.hierarchical_db.get_category_for_template(template_path)
        
        if not category:
            logger.warning(f"Could not determine category for template: {template_path}")
            return []
        
        logger.info(f"Retrieving context from category '{category}' for query: {query[:100]}...")
        
        # Search in the relevant category
        results = self.hierarchical_db.search_category(query, category, k)
        
        if results:
            logger.info(f"Found {len(results)} relevant documents from category '{category}'")
        else:
            logger.warning(f"No relevant documents found in category '{category}'")
        
        return results
    
    def search_all_categories(self, query: str, k: int = 5) -> Dict[str, List[Document]]:
        """
        Search across all categories (for debugging or general search)
        
        Args:
            query: Query string
            k: Number of documents to retrieve per category
            
        Returns:
            Dictionary mapping category names to lists of documents
        """
        if not self._initialized:
            self.initialize()
        
        results = {}
        
        for category_name in self.hierarchical_db.category_structure.keys():
            category_results = self.hierarchical_db.search_category(query, category_name, k)
            if category_results:
                results[category_name] = category_results
        
        return results
    
    def get_available_categories(self) -> List[str]:
        """
        Get list of available categories
        
        Returns:
            List of category names
        """
        if not self._initialized:
            self.initialize()
        
        categories = []
        
        def collect_categories(structure: Dict[str, Any], prefix: str = ""):
            for name, info in structure.items():
                full_name = f"{prefix}/{name}" if prefix else name
                categories.append(full_name)
                
                # Recursively collect subcategories
                if "subcategories" in info and info["subcategories"]:
                    collect_categories(info["subcategories"], full_name)
        
        collect_categories(self.hierarchical_db.category_structure)
        return categories
    
    def get_category_info(self, category_path: str) -> Optional[Dict[str, Any]]:
        """
        Get information about a specific category
        
        Args:
            category_path: Path to the category (e.g., "Notice/Check Bounce")
            
        Returns:
            Category information dictionary or None if not found
        """
        if not self._initialized:
            self.initialize()
        
        # Navigate to the category in the structure
        parts = category_path.split("/")
        current = self.hierarchical_db.category_structure
        
        for part in parts:
            if part in current:
                current = current[part]
            elif "subcategories" in current and part in current["subcategories"]:
                current = current["subcategories"][part]
            else:
                return None
        
        return current
    
    def rebuild_category(self, category_path: str):
        """
        Rebuild vector store for a specific category
        
        Args:
            category_path: Path to the category to rebuild
        """
        if not self._initialized:
            self.initialize()
        
        logger.info(f"Rebuilding vector store for category: {category_path}")
        
        # Get category info
        category_info = self.get_category_info(category_path)
        if not category_info:
            logger.error(f"Category not found: {category_path}")
            return
        
        # Extract category name and parent path
        parts = category_path.split("/")
        category_name = parts[-1]
        parent_path = "/".join(parts[:-1]) if len(parts) > 1 else ""
        
        # Rebuild the category
        self.hierarchical_db._vectorize_category(category_name, category_info, parent_path)
        
        logger.info(f"Successfully rebuilt category: {category_path}")
    
    def get_statistics(self) -> Dict[str, Any]:
        """
        Get statistics about the master database
        
        Returns:
            Dictionary with statistics
        """
        if not self._initialized:
            self.initialize()
        
        stats = {
            "total_categories": 0,
            "total_subcategories": 0,
            "total_files": 0,
            "categories": {}
        }
        
        def count_recursive(structure: Dict[str, Any], prefix: str = ""):
            for name, info in structure.items():
                full_name = f"{prefix}/{name}" if prefix else name
                stats["total_categories"] += 1
                
                file_count = len(info.get("files", []))
                subcategory_count = len(info.get("subcategories", {}))
                
                stats["total_files"] += file_count
                stats["total_subcategories"] += subcategory_count
                
                stats["categories"][full_name] = {
                    "files": file_count,
                    "subcategories": subcategory_count
                }
                
                # Recursively count subcategories
                if info.get("subcategories"):
                    count_recursive(info["subcategories"], full_name)
        
        count_recursive(self.hierarchical_db.category_structure)
        
        return stats
