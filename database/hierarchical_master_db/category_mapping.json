{"Deed": {"path": "master_db/Deed", "subcategories": {"Partnership Deed": {"path": "master_db/Deed/Partnership Deed", "subcategories": {}, "files": [{"name": "ilide.info-partnership-deed-2k18lwum01001-2-pr_1a5ca740ea6a71b109489c18714d78ab.pdf", "path": "master_db/Deed/Partnership Deed/ilide.info-partnership-deed-2k18lwum01001-2-pr_1a5ca740ea6a71b109489c18714d78ab.pdf", "extension": ".pdf"}, {"name": "ilide.info-deed-of-partnership-pr_bb7806add197a12259f9cbadb674022f.pdf", "path": "master_db/Deed/Partnership Deed/ilide.info-deed-of-partnership-pr_bb7806add197a12259f9cbadb674022f.pdf", "extension": ".pdf"}, {"name": "Sample 2.pdf", "path": "master_db/Deed/Partnership Deed/Sample 2.pdf", "extension": ".pdf"}]}, "Deed of exchange": {"path": "master_db/Deed/Deed of exchange", "subcategories": {}, "files": [{"name": "Unequal.pdf", "path": "master_db/Deed/Deed of exchange/Unequal.pdf", "extension": ".pdf"}, {"name": "Sample 1.pdf", "path": "master_db/Deed/Deed of exchange/Sample 1.pdf", "extension": ".pdf"}]}, "Sale Deeds": {"path": "master_db/Deed/Sale Deeds", "subcategories": {}, "files": [{"name": "Sale deed 1.pdf", "path": "master_db/Deed/Sale Deeds/Sale deed 1.pdf", "extension": ".pdf"}, {"name": "Sale deed 2.pdf", "path": "master_db/Deed/Sale Deeds/Sale deed 2.pdf", "extension": ".pdf"}]}, "Dissolution of Partnership": {"path": "master_db/Deed/Dissolution of Partnership", "subcategories": {}, "files": [{"name": "Sample 2.pdf", "path": "master_db/Deed/Dissolution of Partnership/Sample 2.pdf", "extension": ".pdf"}, {"name": "ilide.info-23-dissolution-of-partnership-pr_a3d15498c52802005305e01b7ef54a0f.pdf", "path": "master_db/Deed/Dissolution of Partnership/ilide.info-23-dissolution-of-partnership-pr_a3d15498c52802005305e01b7ef54a0f.pdf", "extension": ".pdf"}]}, "Lease Deed": {"path": "master_db/Deed/<PERSON>se <PERSON>d", "subcategories": {}, "files": [{"name": "Sample 2.pdf", "path": "master_db/Deed/<PERSON>se Deed/Sample 2.pdf", "extension": ".pdf"}, {"name": "Sample 3.pdf", "path": "master_db/Deed/<PERSON>se Deed/Sample 3.pdf", "extension": ".pdf"}, {"name": "Sample 1.pdf", "path": "master_db/Deed/<PERSON>se Deed/Sample 1.pdf", "extension": ".pdf"}]}, "Family Settlement": {"path": "master_db/Deed/Family Settlement", "subcategories": {}, "files": [{"name": "ec39bd21-bfc3-4cc5-b285-b3755a1e2bba.pdf", "path": "master_db/Deed/Family Settlement/ec39bd21-bfc3-4cc5-b285-b3755a1e2bba.pdf", "extension": ".pdf"}]}, "Deed of relinquishment": {"path": "master_db/Deed/Deed of relinquishment", "subcategories": {}, "files": [{"name": "ilide.info-relinquishment-deed25-pr_8c98f747918f4608aaed92fb0d2b7e4f.pdf", "path": "master_db/Deed/Deed of relinquishment/ilide.info-relinquishment-deed25-pr_8c98f747918f4608aaed92fb0d2b7e4f.pdf", "extension": ".pdf"}]}, "Gift Deed": {"path": "master_db/Deed/Gift Deed", "subcategories": {}, "files": [{"name": "Gift Deed DB-1.docx", "path": "master_db/Deed/Gift Deed/Gift Deed DB-1.docx", "extension": ".docx"}, {"name": "Gift Deed GB-2.pdf", "path": "master_db/Deed/Gift Deed/Gift Deed GB-2.pdf", "extension": ".pdf"}]}, "Mortgage": {"path": "master_db/Deed/Mortgage", "subcategories": {"Conditional Mortgage": {"path": "master_db/Deed/Mortgage/Conditional Mortgage", "subcategories": {}, "files": [{"name": "Conditional sale deed 1.pdf", "path": "master_db/Deed/Mortgage/Conditional Mortgage/Conditional sale deed 1.pdf", "extension": ".pdf"}]}, "English Mortgage": {"path": "master_db/Deed/Mortgage/English Mortgage", "subcategories": {}, "files": [{"name": "English Mortgage.pdf", "path": "master_db/Deed/Mortgage/English Mortgage/English Mortgage.pdf", "extension": ".pdf"}]}, "Simple Mortgage": {"path": "master_db/Deed/Mortgage/Simple Mortgage", "subcategories": {}, "files": [{"name": "Simple Mortgage.docx", "path": "master_db/Deed/Mortgage/Simple Mortgage/Simple Mortgage.docx", "extension": ".docx"}, {"name": "ilide.info-mortgage-deed-manjula-pr_125c406ecb07ac7086473547125ad125.pdf", "path": "master_db/Deed/Mortgage/Simple Mortgage/ilide.info-mortgage-deed-manjula-pr_125c406ecb07ac7086473547125ad125.pdf", "extension": ".pdf"}, {"name": "Sample 2.pdf", "path": "master_db/Deed/Mortgage/Simple Mortgage/Sample 2.pdf", "extension": ".pdf"}]}}, "files": [{"name": "Check Once.pdf", "path": "master_db/Deed/Mortgage/Check Once.pdf", "extension": ".pdf"}, {"name": "Mortgage Temp.pdf", "path": "master_db/Deed/Mortgage/Mortgage Temp.pdf", "extension": ".pdf"}, {"name": "Deed of Usufructuary Mortgage by Conditional Sale (Anomalous Mortgage).pdf", "path": "master_db/Deed/Mortgage/Deed of Usufructuary Mortgage by Conditional Sale (Anomalous Mortgage).pdf", "extension": ".pdf"}]}, "Trust Deed": {"path": "master_db/Deed/Trust Deed", "subcategories": {}, "files": [{"name": "Sample 2.pdf", "path": "master_db/Deed/Trust Deed/Sample 2.pdf", "extension": ".pdf"}, {"name": "Sample 1.pdf", "path": "master_db/Deed/Trust Deed/Sample 1.pdf", "extension": ".pdf"}]}}, "files": []}, "Affidavit": {"path": "master_db/Affidavit", "subcategories": {}, "files": [{"name": "pages_0001.pdf", "path": "master_db/Affidavit/pages_0001.pdf", "extension": ".pdf"}, {"name": "Legal Affidavit.pdf", "path": "master_db/Affidavit/Legal Affidavit.pdf", "extension": ".pdf"}]}, "Assignment": {"path": "master_db/Assignment", "subcategories": {}, "files": [{"name": "Deed of Assignment of a Patent.pdf", "path": "master_db/Assignment/Deed of Assignment of a Patent.pdf", "extension": ".pdf"}, {"name": "Assignment-Agreement-between-ZEE-and-Novex.pdf", "path": "master_db/Assignment/Assignment-Agreement-between-ZEE-and-Novex.pdf", "extension": ".pdf"}, {"name": "Assignment of Business Debts.docx", "path": "master_db/Assignment/Assignment of Business Debts.docx", "extension": ".docx"}, {"name": "Patent Assignment Agreement.pdf", "path": "master_db/Assignment/Patent Assignment Agreement.pdf", "extension": ".pdf"}, {"name": "Trademark Assignment Agreement.pdf", "path": "master_db/Assignment/Trademark Assignment Agreement.pdf", "extension": ".pdf"}]}, "Notice": {"path": "master_db/Notice", "subcategories": {"Check Bounce": {"path": "master_db/Notice/Check <PERSON>", "subcategories": {}, "files": [{"name": "<PERSON> 2 (best).pdf", "path": "master_db/Notice/Check <PERSON>ce/Check Bounce 2 (best).pdf", "extension": ".pdf"}, {"name": "Cheque Bounce.pdf", "path": "master_db/Notice/Check <PERSON>/Cheque Bo<PERSON>ce.pdf", "extension": ".pdf"}]}, "Notice of Ejectment": {"path": "master_db/Notice/Notice of Ejectment", "subcategories": {}, "files": []}, "Defective product": {"path": "master_db/Notice/Defective product", "subcategories": {}, "files": [{"name": "ilide.info-format-legal-notice-pr_5b993d263b284f1f5ca6b16317b20152.pdf", "path": "master_db/Notice/Defective product/ilide.info-format-legal-notice-pr_5b993d263b284f1f5ca6b16317b20152.pdf", "extension": ".pdf"}, {"name": "<PERSON><PERSON><PERSON> - Legal Notice.pdf", "path": "master_db/Notice/Defective product/<PERSON><PERSON><PERSON> - Legal Notice.pdf", "extension": ".pdf"}, {"name": "Legal_notice_for_Consumer_Complaint.pdf", "path": "master_db/Notice/Defective product/Legal_notice_for_Consumer_Complaint.pdf", "extension": ".pdf"}]}, "Misc": {"path": "master_db/Notice/Misc", "subcategories": {}, "files": [{"name": "Multiple legal notices.pdf", "path": "master_db/Notice/Misc/Multiple legal notices.pdf", "extension": ".pdf"}]}, "Recovery of dues": {"path": "master_db/Notice/Recovery of dues", "subcategories": {}, "files": [{"name": "Recovery of Dues.pdf", "path": "master_db/Notice/Recovery of dues/Recovery of Dues.pdf", "extension": ".pdf"}, {"name": "Recovery of dues 2.pdf", "path": "master_db/Notice/Recovery of dues/Recovery of dues 2.pdf", "extension": ".pdf"}]}}, "files": [{"name": "Eviction Legal 1.pdf", "path": "master_db/Notice/Eviction Legal 1.pdf", "extension": ".pdf"}, {"name": "Eviction notice 2.pdf", "path": "master_db/Notice/Eviction notice 2.pdf", "extension": ".pdf"}]}}