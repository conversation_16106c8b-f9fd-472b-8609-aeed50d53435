# Legal Document Generator v2.0

A comprehensive AI-powered legal document generation system that uses multiple specialized agents to create, verify, and manage legal documents. The system leverages Azure OpenAI services and implements a sophisticated multi-agent architecture with **hierarchical vectorization** for intelligent document processing and targeted RAG (Retrieval-Augmented Generation).

## 🆕 New Features (v2.0)

### Hierarchical Vector Database
- **Category-Aware Vectorization**: Master database is now vectorized with separate vector stores for each category and subcategory
- **Targeted RAG Retrieval**: When you select a template, only the relevant section of the master database is used for context
- **Tree Structure Preservation**: The hierarchical structure of your master database is maintained in the vector stores
- **Improved Context Quality**: More relevant and focused context retrieval for document generation

### Enhanced Form Creation
- **Smart Placeholder Detection**: Improved detection of various placeholder formats (brackets, underscores, braces)
- **LLM-Powered Field Analysis**: Advanced field type detection and validation using AI
- **Robust Error Handling**: Fallback mechanisms when LLM analysis fails
- **Better Field Naming**: Intelligent conversion of placeholder names to user-friendly labels

## Features

### Core Functionality
- **Document Category Selection**: Choose from various categories like agreements, contracts, notices, etc.
- **Template Library**: A well curated and comprehensive library of legal templates supporting TXT, PDF, and DOCX formats
- **Template Search**: Search for specific template types using keywords
- **Multiple File Format Support**: Upload and process client files in TXT, DOCX, and PDF formats
- **Custom Instructions**: Add specific clauses or instructions to customize documents
- **AI-Powered Document Composition**: Generate professional legal documents using Azure OpenAI GPT-4
- **Document Preview & Revision**: Review and revise generated documents before finalizing
- **Document Export**: Download final documents in multiple formats including TXT, DOCX, and PDF

### Advanced Features
- **Hierarchical Master Database**: Organized vector stores for targeted context retrieval
- **Smart Form Generation**: AI-powered form field detection and creation
- **Rate-Limited Processing**: Intelligent batch processing to handle Azure OpenAI rate limits
- **Document Verification**: AI-powered verification against master database and client files
- **Context-Aware RAG**: Retrieval system that uses only relevant database sections

## 🚀 Quick Start (v2.0)

### 1. System Initialization
Before using the application, you need to initialize the new hierarchical vectorization system:

```bash
# Initialize the system (first time setup)
python initialize_system.py

# Check system status
python initialize_system.py --status

# Force rebuild if needed
python initialize_system.py --force
```

### 2. Run the Application
```bash
# Start the Streamlit application
streamlit run app.py
```

### 3. Using the New Features

#### Hierarchical Vectorization
- The system automatically detects your master database structure
- Creates separate vector stores for each category (e.g., Notice, Deed, Assignment)
- When you select a template, only the relevant category is used for RAG

#### Enhanced Form Creation
- Upload or select a template
- The system now detects various placeholder formats:
  - `[field_name]` - Square brackets
  - `_____` - Underscores
  - `{field_name}` - Curly braces
  - `((field_name))` - Double parentheses
- AI analyzes the template and creates appropriate form fields
- Fallback system ensures forms are created even if AI analysis fails

## 🏗️ System Architecture

### Hierarchical Vector Database Structure
```
database/
├── hierarchical_master_db/
│   ├── Notice/                    # Category-specific vector store
│   ├── Notice_Check_Bounce/       # Subcategory-specific vector store
│   ├── Deed/
│   ├── Deed_Sale_Deeds/
│   └── category_mapping.json      # Category structure mapping
└── client_files/                  # Session-specific client files
```

### Master Database Structure
```
master_db/
├── Notice/
│   ├── Check Bounce/
│   ├── Eviction/
│   └── Recovery of dues/
├── Deed/
│   ├── Sale Deeds/
│   ├── Gift Deed/
│   └── Partnership Deed/
└── Assignment/
    ├── Patent Assignment/
    └── Trademark Assignment/
```



Agents

User Data Agent:

Extracts relevant clauses and contextual information from client files (TXT, DOCX, PDF) using vector search to create a temporary knowledge base for document drafting. Processes multiple file formats to provide context for document generation.

Master DB Agent:

Searches the master database for relevant legal clauses and precedents and identifies clauses that are appropriate for the specific document type and context

Form Creator Agent:

Analyzes templates to identify required fields and creates intuitive forms with fields grouped by category. Forms are created with appropriate field types based on content

Document Composer Agent:

Combines the template, form data, and relevant clauses into a cohesive document. It accepts feedback and revises the doc.

Verification Agent:

Analyzes the generated document for correctness, consistency, and potential legal issues. It identifies red flags, inconsistencies, and errors, categorizing them by severity (Critical, Important, Minor) and providing suggestions for improvement.





File Structure

Legtech2/
├── app.py                                       # Main Streamlit application with UI components and workflow
├── agents/                                    # CrewAI agents for different document generation tasks
│   ├── __init__.py                      # Package initialization
│   ├── user_data_agent.py      #Agent for extracting client information from documents
│   ├── master_db_agent.py      # Agent for finding relevant clauses from master database
│   ├── form_creator_agent.py   # Agent for generating forms based on template fields
│   ├── document_composer_agent.py # Agent for composing and revising documents
│   ├── verification_agent.py   # Agent for verifying document correctness and identifying issues
├── utils/                       # Utility functions and helper classes
│   ├── __init__.py             # Package initialization
│   ├── api_key.py              # Utility for managing API keys
│   ├── template_manager.py      # Template management, categorization, and field extraction
│   ├── document_processor.py   # Document processing, pattern matching, and template filling
│   ├── document_converter.py   # Handles conversion between different document formats (txt, docx, pdf)
├── database/                   # Database implementations
│   ├── __init__.py             # Package initialization
│   ├── vector_db.py            # Vector database using FAISS for semantic search
├── .env                        # Environment variables for API keys
├── requirements.txt            # Project dependencies
├── run.sh                      # Script to run the application with virtual environment
├── test_setup.py               # Script to test the environment setup
├── client_files/                # Client data organized by client name
│   ├── [Client Name]/           # Directory for each client
│       ├── [Document].[txt|docx|pdf]  # Client documents in various formats
├── master_db/                  # Master database of legal clauses and precedents
│   ├── [Category]/              # Organized by document category
│       ├── [Document].txt       # Reference documents in text format
├── templates/                  # Document templates organized by category
│   ├── [Category]/              # Directory for each document category
│       ├── [Template].txt      # Template files with placeholders in [field_name] format






Key Components

Main Application (app.py)

The main Streamlit application that handles the user interface and workflow and orchestrates the different steps of the document generation process.

Agents

User Data Agent (agents/user_data_agent.py)

Master DB Agent (agents/master_db_agent.py)

Form Creator Agent (agents/form_creator_agent.py)

Document Composer Agent (agents/document_composer_agent.py)

Verification Agent (agents/verification_agent.py)

Utilities

Template Manager (utils/template_manager.py)

Manages template categorization and retrieval, search functionality, and extracts fields using regex and handles content loading

Document Processor (utils/document_processor.py)

Extracts information from documents using pattern matching, fills templates with form data

Document Converter (utils/document_converter.py)

Handles conversion between different document formats (TXT, DOCX, PDF) for both uploading and downloading

API Key Utility (utils/api_key.py)

Manages API key retrieval from environment variables and consistent API key usage across the application

Database

Vector Database (database/vector_db.py)

Implements vector storage using FAISS, semantic search functionality, document indexing and chunking, Google's embedding model for vectorization. Supports indexing of multiple file formats (TXT, DOCX, PDF).



Setup and Installation

Prerequisites

Python 3.8 or higher

Google Gemini API key (Get it from AI Studio)

Installation Steps

Clone the repository

Create a .env file with your API keys

Virtual Environment

Creates a dedicated virtual environment for the application to ensuring dependency isolation.





Dependencies

streamlit: Web application framework

crewai: AI agent framework

langchain: LLM application framework

langchain-openai: Azure OpenAI integration

faiss-cpu: Vector similarity search

openai: Azure OpenAI Python client

numpy: Numerical computing

pandas: Data manipulation

python-dotenv: Environment variable management

pydantic: Data validation

python-docx: Microsoft Word document processing

docx2txt: DOCX text extraction

pypdf: PDF text extraction

reportlab: PDF generation

pypdf

reportlab

All dependencies are specified in the requirements.txt file





