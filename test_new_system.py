#!/usr/bin/env python3
"""
Test Script for New Hierarchical Vectorization and Form Creation System

This script tests the key components of the new system to ensure everything is working correctly.
"""

import os
import sys
import logging
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Add the current directory to Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from utils.vectorization_manager import VectorizationManager
from agents.form_creator_agent import FormCreatorAgent
from utils.template_manager import TemplateManager

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


def test_hierarchical_vectorization():
    """Test the hierarchical vectorization system"""
    print("\n🧪 Testing Hierarchical Vectorization System")
    print("-" * 50)
    
    try:
        manager = VectorizationManager()
        
        # Check system status
        status = manager.get_system_status()
        print(f"✓ System Status: {status['status']}")
        
        if status['status'] != 'initialized':
            print("❌ System not initialized. Please run: python initialize_system.py")
            return False
        
        # Test template mapping
        print("\n📋 Testing Template Mapping...")
        template_mapping = manager.get_template_categories_mapping()
        
        mapped_count = sum(1 for category in template_mapping.values() if category != "unmapped")
        total_count = len(template_mapping)
        
        print(f"✓ Template Mapping: {mapped_count}/{total_count} templates mapped")
        
        # Test context retrieval with a few templates
        print("\n🔍 Testing Context Retrieval...")
        test_count = 0
        success_count = 0
        
        for template_path, category in list(template_mapping.items())[:3]:
            if category != "unmapped":
                test_count += 1
                result = manager.test_context_retrieval(template_path, "legal clause", k=2)
                
                if result['success'] and result['results_count'] > 0:
                    success_count += 1
                    print(f"✓ {os.path.basename(template_path)}: {result['results_count']} documents retrieved")
                else:
                    print(f"❌ {os.path.basename(template_path)}: Failed to retrieve context")
        
        print(f"✓ Context Retrieval Success Rate: {success_count}/{test_count}")
        
        return success_count > 0
        
    except Exception as e:
        print(f"❌ Hierarchical vectorization test failed: {e}")
        return False


def test_form_creation():
    """Test the enhanced form creation system"""
    print("\n🧪 Testing Enhanced Form Creation System")
    print("-" * 50)
    
    try:
        form_creator = FormCreatorAgent()
        template_manager = TemplateManager()
        
        # Test with different placeholder formats
        test_templates = [
            {
                "name": "Square Brackets Template",
                "content": """
                This is a legal document for [client_name] dated [date].
                The amount is [amount] and the address is [client_address].
                Email: [email_address] Phone: [phone_number]
                """
            },
            {
                "name": "Mixed Placeholders Template", 
                "content": """
                Agreement between [party_1] and {party_2}.
                Date: _____ Amount: ((total_amount))
                Description: [description_of_services]
                """
            },
            {
                "name": "Underscore Template",
                "content": """
                Name: _____________
                Date: _____________
                Amount: ___________
                Address: __________
                """
            }
        ]
        
        success_count = 0
        
        for template in test_templates:
            print(f"\n📝 Testing: {template['name']}")
            
            try:
                # Test placeholder extraction
                placeholders = form_creator._extract_placeholders(template['content'])
                print(f"  ✓ Extracted {len(placeholders)} placeholders: {placeholders}")
                
                # Test form field creation
                form_fields = form_creator.create_form_fields(template['content'])
                print(f"  ✓ Created {len(form_fields)} form fields")
                
                # Display sample fields
                for field in form_fields[:3]:  # Show first 3 fields
                    print(f"    - {field['label']} ({field['type']}) - Required: {field.get('required', False)}")
                
                if form_fields:
                    success_count += 1
                
            except Exception as e:
                print(f"  ❌ Failed to process template: {e}")
        
        print(f"\n✓ Form Creation Success Rate: {success_count}/{len(test_templates)}")
        
        return success_count > 0
        
    except Exception as e:
        print(f"❌ Form creation test failed: {e}")
        return False


def test_real_templates():
    """Test with real templates from the templates directory"""
    print("\n🧪 Testing with Real Templates")
    print("-" * 50)
    
    try:
        template_manager = TemplateManager()
        form_creator = FormCreatorAgent()
        
        # Get a few real templates
        categories = template_manager.get_categories()
        test_templates = []
        
        for category in categories[:2]:  # Test first 2 categories
            templates = template_manager.get_templates(category)
            if templates:
                test_templates.extend(templates[:2])  # Take first 2 templates from each category
        
        if not test_templates:
            print("❌ No templates found to test")
            return False
        
        success_count = 0
        
        for template in test_templates[:3]:  # Test first 3 templates
            print(f"\n📄 Testing: {template['name']}")
            
            try:
                # Get template content
                content = template_manager.get_template_content(template['path'])
                print(f"  ✓ Loaded template content ({len(content)} characters)")
                
                # Test form field creation
                form_fields = form_creator.create_form_fields(content)
                print(f"  ✓ Created {len(form_fields)} form fields")
                
                if form_fields:
                    success_count += 1
                    # Show sample fields
                    for field in form_fields[:2]:
                        print(f"    - {field['label']} ({field['type']})")
                else:
                    print("  ⚠️  No form fields created")
                
            except Exception as e:
                print(f"  ❌ Failed to process template: {e}")
        
        print(f"\n✓ Real Template Success Rate: {success_count}/{min(len(test_templates), 3)}")
        
        return success_count > 0
        
    except Exception as e:
        print(f"❌ Real template test failed: {e}")
        return False


def run_comprehensive_test():
    """Run comprehensive test of all new features"""
    print("🚀 Comprehensive Test of New System Features")
    print("=" * 60)
    
    test_results = []
    
    # Test 1: Hierarchical Vectorization
    result1 = test_hierarchical_vectorization()
    test_results.append(("Hierarchical Vectorization", result1))
    
    # Test 2: Form Creation
    result2 = test_form_creation()
    test_results.append(("Enhanced Form Creation", result2))
    
    # Test 3: Real Templates
    result3 = test_real_templates()
    test_results.append(("Real Template Processing", result3))
    
    # Summary
    print("\n📊 Test Summary")
    print("=" * 30)
    
    passed = 0
    for test_name, result in test_results:
        status = "✅ PASSED" if result else "❌ FAILED"
        print(f"{test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\nOverall: {passed}/{len(test_results)} tests passed")
    
    if passed == len(test_results):
        print("\n🎉 All tests passed! The new system is ready to use.")
        print("\nNext steps:")
        print("1. Run: streamlit run app.py")
        print("2. Select a template and test the new form creation")
        print("3. Generate a document to test hierarchical RAG")
    else:
        print("\n⚠️  Some tests failed. Please check the logs and fix issues before proceeding.")
    
    return passed == len(test_results)


def main():
    """Main test function"""
    import argparse
    
    parser = argparse.ArgumentParser(description="Test the new hierarchical vectorization and form creation system")
    parser.add_argument("--vectorization", action="store_true", help="Test only hierarchical vectorization")
    parser.add_argument("--forms", action="store_true", help="Test only form creation")
    parser.add_argument("--templates", action="store_true", help="Test only real templates")
    
    args = parser.parse_args()
    
    if args.vectorization:
        test_hierarchical_vectorization()
    elif args.forms:
        test_form_creation()
    elif args.templates:
        test_real_templates()
    else:
        run_comprehensive_test()


if __name__ == "__main__":
    main()
