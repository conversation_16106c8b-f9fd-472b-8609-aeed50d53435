import re
from typing import Dict, List, Any

class DocumentProcessor:
    @staticmethod
    def extract_user_info(documents: List[Dict[str, Any]]) -> Dict[str, str]:
        """
        Extract user information from documents

        Args:
            documents: List of documents with content and metadata

        Returns:
            Dictionary of user information
        """
        user_info = {}

        # Common patterns for user information
        patterns = {
            "name": r"(?:Mr\.|Mrs\.|Ms\.|Dr\.)?\s*([A-Z][a-z]+ [A-Z][a-z]+)",
            "address": r"(?:residing at|located at|address:?)\s*([^,\.]+(?:,\s*[^,\.]+){1,3})",
            "email": r"([a-zA-Z0-9_.+-]+@[a-zA-Z0-9-]+\.[a-zA-Z0-9-.]+)",
            "phone": r"(?:\+\d{1,3}[-\s]?)?\(?\d{3}\)?[-\s]?\d{3}[-\s]?\d{4}",
            "company": r"(?:company|employer|organization):?\s*([A-Z][a-zA-Z0-9\s]+(?:Pvt\.|Ltd\.|Inc\.|LLC)?)",
            "position": r"(?:position|job title|designation|role):?\s*([A-Z][a-zA-Z0-9\s]+)",
            "salary": r"(?:salary|compensation|pay):?\s*(?:Rs\.|₹|INR)?\s*(\d{1,3}(?:,\d{3})*(?:\.\d{2})?)",
            "date": r"(?:date|effective date|start date):?\s*(\d{1,2}[\/\-\.]\d{1,2}[\/\-\.]\d{2,4})",
        }

        for doc in documents:
            content = doc["content"]

            for info_type, pattern in patterns.items():
                if info_type not in user_info:
                    matches = re.findall(pattern, content, re.IGNORECASE)
                    if matches:
                        user_info[info_type] = matches[0]

        return user_info

    @staticmethod
    def fill_template(template: str, field_values: Dict[str, Any]) -> str:
        """
        Fill a template with field values

        Args:
            template: Template content
            field_values: Dictionary of field values (can be any type)

        Returns:
            Filled template
        """
        print(f"DEBUG DocumentProcessor: Starting template fill")
        print(f"DEBUG DocumentProcessor: Template length: {len(template)}")
        print(f"DEBUG DocumentProcessor: Field values: {field_values}")
        
        # Filter out empty or None values
        filtered_values = {k: v for k, v in field_values.items() 
                          if v is not None and str(v).strip() != ""}
        print(f"DEBUG DocumentProcessor: Filtered values (non-empty): {filtered_values}")
        
        filled_template = template

        # First, handle standard bracket placeholders [field_name]
        bracket_count = 0
        for field, value in filtered_values.items():
            # Convert value to string to ensure it can be used in replace
            str_value = str(value) if value is not None else ""
            placeholder = f"[{field}]"
            if placeholder in filled_template:
                filled_template = filled_template.replace(placeholder, str_value)
                bracket_count += 1
                print(f"DEBUG DocumentProcessor: Replaced {placeholder} with '{str_value}'")
        
        print(f"DEBUG DocumentProcessor: Replaced {bracket_count} bracket placeholders")

        # Handle dotted placeholders (…………………) by mapping field names to positions
        original_length = len(filled_template)
        filled_template = DocumentProcessor._fill_dotted_placeholders(filled_template, filtered_values)
        
        if len(filled_template) != original_length:
            print(f"DEBUG DocumentProcessor: Dotted placeholders changed template length from {original_length} to {len(filled_template)}")
        else:
            print(f"DEBUG DocumentProcessor: No dotted placeholders were filled")

        print(f"DEBUG DocumentProcessor: Final template length: {len(filled_template)}")
        return filled_template

    @staticmethod
    def _fill_dotted_placeholders(template: str, field_values: Dict[str, Any]) -> str:
        """
        Fill dotted placeholders based on contextual field names
        """
        # Create field mapping based on position and context
        field_mapping = DocumentProcessor._create_field_position_mapping(template, field_values)
        
        # Replace placeholders from end to beginning to maintain positions
        for position_info in reversed(field_mapping):
            start, end, field_value = position_info['start'], position_info['end'], position_info['value']
            template = template[:start] + field_value + template[end:]
            
        return template

    @staticmethod
    def _create_field_position_mapping(template: str, field_values: Dict[str, Any]) -> List[Dict]:
        """
        Create mapping between dotted placeholders and field values based on context
        """
        # Find all dotted placeholders with their positions
        dot_pattern = r'…{3,}'
        placeholder_positions = []
        
        for match in re.finditer(dot_pattern, template):
            placeholder_positions.append({
                'start': match.start(),
                'end': match.end(),
                'line_context': DocumentProcessor._get_line_context(template, match.start()),
                'surrounding_context': DocumentProcessor._get_surrounding_context(template, match.start(), 100)
            })
        
        # Create improved context-based field mappings with more specific patterns
        context_field_mapping = {
            # Court information - should be first two placeholders in court heading
            'court of.*at': ['court_name', 'court_location'],
            'in the court of': ['court_name'],
            'at.*court': ['court_location'],
            
            # Affidavit personal details - specific order patterns
            'i,': ['deponent_name'],  # First placeholder after "I,"
            'son.*of|daughter.*of': ['father_name'],  # After "son/daughter of"
            'aged.*years': ['age'],  # After "aged" and before "years"
            'working at': ['workplace'],  # After "working at"
            
            # Verification section
            'verified.*at': ['verification_location'],  # After "verified at"
            'on.*that': ['verification_date'],  # After "on" in verification
            
            # Signature/deponent
            'deponent': ['deponent_name'],
            
            # Agreement/contract patterns
            'party 1.*name': ['party1_name'],
            'party 1.*address': ['party1_address'], 
            'party 1.*phone': ['party1_phone'],
            'party 1.*signature': ['party1_signature'],
            'party 2.*name': ['party2_name'],
            'party 2.*address': ['party2_address'],
            'party 2.*email': ['party2_email'],
            'party 2.*signature': ['party2_signature'],
            'signed at': ['signing_location'],
            'on.*signature': ['signing_date'],
        }
        
        # Map fields to positions with improved logic
        field_position_mapping = []
        available_fields = dict(field_values)
        
        for i, pos_info in enumerate(placeholder_positions):
            context = pos_info['line_context'].lower()
            surrounding = pos_info['surrounding_context'].lower()
            field_value = ""
            matched_field = None
            
            print(f"DEBUG: Processing placeholder {i+1} at position {pos_info['start']}")
            print(f"DEBUG: Line context: '{context}'")
            
            # Strategy 1: Match by specific context patterns
            for pattern, preferred_fields in context_field_mapping.items():
                if re.search(pattern, context) or re.search(pattern, surrounding):
                    print(f"DEBUG: Pattern '{pattern}' matched, trying fields: {preferred_fields}")
                    for field_name in preferred_fields:
                        if field_name in available_fields:
                            field_value = str(available_fields[field_name]) if available_fields[field_name] is not None else ""
                            matched_field = field_name
                            print(f"DEBUG: Using field '{field_name}' = '{field_value}'")
                            break
                    if matched_field:
                        del available_fields[matched_field]
                        break
            
            # Strategy 2: Smart semantic matching based on context keywords
            if not matched_field and available_fields:
                print(f"DEBUG: Trying semantic field matching")
                
                # Look for semantic clues in the context
                semantic_mappings = {
                    'additional': ['additional_info', 'additional_details', 'notes'],
                    'signature': ['signature_name', 'signature', 'signed_by'],
                    'name': ['name', 'full_name', 'person_name'],
                    'address': ['address', 'location', 'place'],
                    'phone': ['phone', 'telephone', 'contact'],
                    'email': ['email', 'email_address'],
                    'age': ['age', 'years'],
                    'location': ['location', 'place', 'city'],
                    'date': ['date', 'when', 'time'],
                }
                
                for semantic_key, field_variants in semantic_mappings.items():
                    if semantic_key in context:
                        for variant in field_variants:
                            if variant in available_fields:
                                field_value = str(available_fields[variant])
                                matched_field = variant
                                print(f"DEBUG: Semantic match - '{semantic_key}' -> '{variant}' = '{field_value}'")
                                del available_fields[variant]
                                break
                        if matched_field:
                            break
            
            # Strategy 3: Direct field name matching in context (for exact matches)
            if not matched_field and available_fields:
                print(f"DEBUG: Trying direct field name matching")
                for field_name in list(available_fields.keys()):
                    # Check various forms of field name but be more strict
                    clean_field = field_name.replace('_', ' ').replace('1', '').replace('2', '').strip()
                    if len(clean_field) > 2 and clean_field in context:
                        field_value = str(available_fields[field_name]) if available_fields[field_name] is not None else ""
                        matched_field = field_name
                        print(f"DEBUG: Direct match - field '{field_name}' (clean: '{clean_field}') = '{field_value}'")
                        del available_fields[field_name]
                        break
            
            # Strategy 4: Smart positional matching based on common legal document structure
            if not matched_field and available_fields:
                print(f"DEBUG: Using positional strategy for placeholder {i+1}")
                # First few placeholders are usually court info
                if i < 2 and any(f in available_fields for f in ['court_name', 'court_location']):
                    if 'court_name' in available_fields:
                        field_value = str(available_fields['court_name'])
                        matched_field = 'court_name'
                        del available_fields['court_name']
                    elif 'court_location' in available_fields:
                        field_value = str(available_fields['court_location'])
                        matched_field = 'court_location'
                        del available_fields['court_location']
                # Use first available field as fallback, but try to be smart about it
                else:
                    # Prefer fields that seem most relevant
                    priority_order = ['deponent_name', 'name', 'father_name', 'age', 'workplace', 'address', 
                                    'verification_location', 'verification_date', 'signature_name']
                    
                    found_priority_field = False
                    for priority_field in priority_order:
                        if priority_field in available_fields:
                            field_value = str(available_fields[priority_field])
                            matched_field = priority_field
                            del available_fields[priority_field]
                            found_priority_field = True
                            break
                    
                    if not found_priority_field and available_fields:
                        field_name, value = next(iter(available_fields.items()))
                        field_value = str(value) if value is not None else ""
                        matched_field = field_name
                        del available_fields[field_name]
                        
                print(f"DEBUG: Positional match - using '{matched_field}' = '{field_value}'")
            
            # If still no match, leave empty
            if not matched_field:
                print(f"DEBUG: No match found for placeholder {i+1}, leaving empty")
                field_value = ""
            
            field_position_mapping.append({
                'start': pos_info['start'],
                'end': pos_info['end'],
                'value': field_value
            })
        
        return field_position_mapping

    @staticmethod
    def _get_line_context(template: str, position: int) -> str:
        """
        Get the line context around a position in the template
        """
        lines = template.split('\n')
        current_pos = 0
        
        for line in lines:
            line_end = current_pos + len(line)
            if current_pos <= position <= line_end:
                return line.strip()
            current_pos = line_end + 1  # +1 for the newline character
        
        return ""

    @staticmethod
    def _get_surrounding_context(template: str, position: int, radius: int = 50) -> str:
        """
        Get the surrounding context (before and after) around a position in the template
        """
        start = max(0, position - radius)
        end = min(len(template), position + radius)
        return template[start:end].strip()
