import os
import io
from typing import Optional, Union, BinaryIO
import docx
from docx.shared import Pt
from docx.enum.text import WD_PARAGRAPH_ALIGNMENT
import pypdf
import docx2txt

class DocumentConverter:
    """
    Utility class for converting between different document formats.
    Supports text, docx, and PDF formats.
    """

    @staticmethod
    def extract_text_from_file(file_path: str) -> str:
        """
        Extract text from a file based on its extension.

        Args:
            file_path: Path to the file

        Returns:
            Extracted text content
        """
        _, ext = os.path.splitext(file_path)
        ext = ext.lower()

        if ext == '.txt':
            # Text file
            with open(file_path, 'r', encoding='utf-8') as f:
                return f.read()
        elif ext == '.docx':
            # DOCX file
            return docx2txt.process(file_path)
        elif ext == '.pdf':
            # PDF file
            return DocumentConverter.extract_text_from_pdf(file_path)
        else:
            raise ValueError(f"Unsupported file format: {ext}")

    @staticmethod
    def extract_text_from_pdf(file_path: str) -> str:
        """
        Extract text from a PDF file.

        Args:
            file_path: Path to the PDF file

        Returns:
            Extracted text content
        """
        text = ""
        with open(file_path, 'rb') as file:
            pdf_reader = pypdf.PdfReader(file)
            for page_num in range(len(pdf_reader.pages)):
                page = pdf_reader.pages[page_num]
                text += page.extract_text() + "\n\n"
        return text

    @staticmethod
    def text_to_docx(text: str) -> bytes:
        """
        Convert text to a DOCX file.

        Args:
            text: Text content to convert

        Returns:
            DOCX file as bytes
        """
        doc = docx.Document()

        # Set default font
        style = doc.styles['Normal']
        font = style.font
        font.name = 'Times New Roman'
        font.size = Pt(12)

        # Add paragraphs
        for paragraph in text.split('\n'):
            if paragraph.strip():
                p = doc.add_paragraph(paragraph)
                # Check if this is a heading (all caps, short line)
                if paragraph.isupper() and len(paragraph) < 50:
                    p.alignment = WD_PARAGRAPH_ALIGNMENT.CENTER
                    run = p.runs[0]
                    run.bold = True
            else:
                doc.add_paragraph()  # Empty paragraph for line breaks

        # Save to bytes
        file_stream = io.BytesIO()
        doc.save(file_stream)
        file_stream.seek(0)
        return file_stream.getvalue()

    @staticmethod
    def text_to_pdf(text: str) -> bytes:
        """
        Convert text to a PDF file using reportlab.

        Args:
            text: Text content to convert

        Returns:
            PDF file as bytes
        """
        from reportlab.pdfgen import canvas
        from reportlab.lib.pagesizes import letter
        from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
        from reportlab.platypus import SimpleDocTemplate, Paragraph, Spacer
        from reportlab.lib.units import inch
        from reportlab.lib.enums import TA_CENTER, TA_LEFT

        # Create a BytesIO object to store the PDF
        buffer = io.BytesIO()

        # Create the PDF document
        doc = SimpleDocTemplate(
            buffer,
            pagesize=letter,
            rightMargin=72,
            leftMargin=72,
            topMargin=72,
            bottomMargin=72
        )

        # Create styles
        styles = getSampleStyleSheet()
        styles.add(ParagraphStyle(
            name='CustomHeading',
            parent=styles['Heading1'],
            alignment=TA_CENTER,
        ))

        # Modify the existing Normal style instead of adding a new one with the same name
        normal_style = styles['Normal']
        normal_style.fontSize = 12
        normal_style.leading = 14

        # Create the story (content)
        story = []

        # Split text into paragraphs
        paragraphs = text.split('\n\n')

        for paragraph in paragraphs:
            if not paragraph.strip():
                # Add a spacer for empty paragraphs
                story.append(Spacer(1, 12))
                continue

            # Check if this is a heading (all caps, short line)
            lines = paragraph.split('\n')
            for line in lines:
                if line.strip():
                    if line.strip().isupper() and len(line.strip()) < 50:
                        # It's a heading
                        story.append(Paragraph(line.strip(), styles['CustomHeading']))
                        story.append(Spacer(1, 12))
                    else:
                        # Regular text
                        story.append(Paragraph(line.strip(), styles['Normal']))
                        story.append(Spacer(1, 6))

        # Build the PDF
        doc.build(story)

        # Get the PDF data
        buffer.seek(0)
        return buffer.getvalue()
