import os
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

def get_azure_openai_config():
    """
    Get the Azure OpenAI configuration from environment variables
    
    Returns:
        dict: Azure OpenAI configuration
    """
    config = {
        'api_key': os.getenv("AZURE_OPENAI_API_KEY"),
        'endpoint': os.getenv("AZURE_OPENAI_ENDPOINT"),
        'api_version': os.getenv("AZURE_OPENAI_API_VERSION"),
        'deployment_name': os.getenv("AZURE_OPENAI_DEPLOYMENT_NAME"),
        'embedding_deployment': os.getenv("AZURE_OPENAI_EMBEDDING_DEPLOYMENT", "text-embedding-3-small")
    }
    
    required_vars = ['api_key', 'endpoint', 'api_version', 'deployment_name']
    missing_vars = [key for key in required_vars if not config[key]]
    if missing_vars:
        raise ValueError(f"Missing Azure OpenAI environment variables: {', '.join(missing_vars)}")
    
    return config

# Maintain backward compatibility
def get_api_key():
    """
    Get the Azure OpenAI API key from environment variables
    
    Returns:
        str: Azure OpenAI API key
    """
    return get_azure_openai_config()['api_key']
