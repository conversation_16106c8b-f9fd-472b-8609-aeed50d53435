import os
import glob
import re
from typing import List, Dict, Any, Optional, Tuple
from utils.document_converter import DocumentConverter

class TemplateManager:
    def __init__(self, templates_dir: str = "templates"):
        """
        Initialize the template manager

        Args:
            templates_dir: Directory containing templates
        """
        self.templates_dir = templates_dir

    def get_categories(self) -> List[str]:
        """
        Get all template categories

        Returns:
            List of category names
        """
        categories = []
        for item in os.listdir(self.templates_dir):
            if os.path.isdir(os.path.join(self.templates_dir, item)):
                categories.append(item)
        return categories

    def get_templates(self, category: str) -> List[Dict[str, str]]:
        """
        Get all templates in a category (supports txt, pdf, docx)

        Args:
            category: Category name

        Returns:
            List of template information dictionaries
        """
        templates = []
        category_path = os.path.join(self.templates_dir, category)

        if not os.path.exists(category_path):
            return []

        # Support multiple file formats
        supported_extensions = ['*.txt', '*.pdf', '*.docx', '*.doc']
        
        for extension in supported_extensions:
            for file_path in glob.glob(f"{category_path}/**/{extension}", recursive=True):
                file_name = os.path.basename(file_path)
                file_ext = os.path.splitext(file_name)[1].lower()
                
                # Create template name by removing extension and formatting
                template_name = os.path.splitext(file_name)[0].replace("_", " ").title()
                
                # Extract tags from filename (without extension)
                base_name = os.path.splitext(file_name)[0]
                tags = base_name.lower().split("_") if "_" in base_name else [base_name.lower()]

                templates.append({
                    "name": template_name,
                    "path": file_path,
                    "tags": tags,
                    "file_type": file_ext,
                    "category": category
                })

        return templates

    def search_templates(self, category: str, search_term: str) -> List[Dict[str, str]]:
        """
        Search for templates in a category

        Args:
            category: Category name
            search_term: Search term

        Returns:
            List of matching template information dictionaries
        """
        all_templates = self.get_templates(category)
        search_term = search_term.lower()

        return [
            template for template in all_templates
            if search_term in template["name"].lower() or
               any(search_term in tag.lower() for tag in template["tags"])
        ]

    def get_template_content(self, template_path: str) -> str:
        """
        Get the content of a template (supports txt, pdf, docx)

        Args:
            template_path: Path to the template file or a special identifier for custom templates

        Returns:
            Template content
        """
        # Check if this is a custom template from session state
        if template_path.startswith("session_template_"):
            # This is a custom template, get content from session state
            import streamlit as st
            if "custom_template" in st.session_state and st.session_state.custom_template.get("path") == template_path:
                return st.session_state.custom_template.get("content", "")
            else:
                raise ValueError(f"Custom template not found in session state: {template_path}")
        else:
            # Regular template file - handle different formats
            file_ext = os.path.splitext(template_path)[1].lower()
            
            if file_ext == '.txt':
                # Text file - read directly
                try:
                    with open(template_path, "r", encoding='utf-8') as f:
                        return f.read()
                except UnicodeDecodeError:
                    # Fallback to latin-1 if utf-8 fails
                    with open(template_path, "r", encoding='latin-1') as f:
                        return f.read()
            elif file_ext in ['.pdf', '.docx', '.doc']:
                # Use DocumentConverter for PDF and DOCX files
                try:
                    return DocumentConverter.extract_text_from_file(template_path)
                except Exception as e:
                    raise ValueError(f"Could not extract content from {template_path}: {str(e)}")
            else:
                raise ValueError(f"Unsupported template file format: {file_ext}")

    def extract_fields(self, template_content: str) -> List[str]:
        """
        Extract fields from a template

        Args:
            template_content: Template content

        Returns:
            List of field names
        """
        # Find all fields in square brackets or underscores
        fields = re.findall(r'\[(.*?)\]|_+', template_content)
        # Filter out empty matches and normalize underscore fields
        fields = [f if f else "Field" for f in fields]

        # Remove duplicates while preserving order
        unique_fields = []
        for field in fields:
            if field not in unique_fields:
                unique_fields.append(field)

        return unique_fields
