"""
Vectorization Manager Utility

This utility provides functions to initialize, manage, and test the hierarchical
vectorization system for the master database.
"""

import os
import logging
from typing import Dict, List, Any, Optional
from database.master_db_manager import MasterDatabaseManager
from utils.template_manager import TemplateManager

logger = logging.getLogger(__name__)


class VectorizationManager:
    """
    Utility class for managing the hierarchical vectorization system
    """
    
    def __init__(self):
        """Initialize the vectorization manager"""
        self.master_db_manager = MasterDatabaseManager()
        self.template_manager = TemplateManager()
        
    def initialize_system(self, force_rebuild: bool = False) -> bool:
        """
        Initialize the entire vectorization system
        
        Args:
            force_rebuild: Whether to force rebuild all vector stores
            
        Returns:
            True if successful, False otherwise
        """
        try:
            logger.info("Initializing hierarchical vectorization system...")
            
            # Initialize the master database manager
            self.master_db_manager.initialize(force_rebuild=force_rebuild)
            
            # Get and display statistics
            stats = self.master_db_manager.get_statistics()
            logger.info(f"System initialized successfully!")
            logger.info(f"Categories: {stats['total_categories']}")
            logger.info(f"Files processed: {stats['total_files']}")
            
            return True
            
        except Exception as e:
            logger.error(f"Failed to initialize system: {e}")
            return False
    
    def get_system_status(self) -> Dict[str, Any]:
        """
        Get the current status of the vectorization system
        
        Returns:
            Dictionary with system status information
        """
        try:
            # Check if system is initialized
            if not self.master_db_manager._check_vector_stores_exist():
                return {
                    "status": "not_initialized",
                    "message": "Hierarchical vector stores have not been created yet"
                }
            
            # Get statistics
            stats = self.master_db_manager.get_statistics()
            categories = self.master_db_manager.get_available_categories()
            
            return {
                "status": "initialized",
                "statistics": stats,
                "available_categories": categories,
                "message": f"System ready with {len(categories)} categories"
            }
            
        except Exception as e:
            return {
                "status": "error",
                "message": f"Error checking system status: {e}"
            }
    
    def test_template_mapping(self, template_path: str) -> Dict[str, Any]:
        """
        Test template-to-category mapping for a specific template
        
        Args:
            template_path: Path to the template file
            
        Returns:
            Dictionary with mapping results
        """
        try:
            # Get category for template
            category = self.master_db_manager.hierarchical_db.get_category_for_template(template_path)
            
            if not category:
                return {
                    "success": False,
                    "template_path": template_path,
                    "message": "Could not determine category for template"
                }
            
            # Check if vector store exists for this category
            vector_store = self.master_db_manager.hierarchical_db.load_category_vector_store(category)
            
            # Get category info
            category_info = self.master_db_manager.get_category_info(category)
            
            return {
                "success": True,
                "template_path": template_path,
                "mapped_category": category,
                "vector_store_exists": vector_store is not None,
                "category_info": {
                    "files_count": len(category_info.get("files", [])) if category_info else 0,
                    "subcategories_count": len(category_info.get("subcategories", {})) if category_info else 0
                }
            }
            
        except Exception as e:
            return {
                "success": False,
                "template_path": template_path,
                "message": f"Error testing template mapping: {e}"
            }
    
    def test_context_retrieval(self, template_path: str, query: str, k: int = 3) -> Dict[str, Any]:
        """
        Test context retrieval for a template and query
        
        Args:
            template_path: Path to the template file
            query: Query string
            k: Number of documents to retrieve
            
        Returns:
            Dictionary with retrieval results
        """
        try:
            # Get relevant context
            documents = self.master_db_manager.get_relevant_context(template_path, query, k)
            
            # Format results
            results = []
            for doc in documents:
                results.append({
                    "content_preview": doc.page_content[:200] + "..." if len(doc.page_content) > 200 else doc.page_content,
                    "source": doc.metadata.get("source", ""),
                    "category": doc.metadata.get("category", ""),
                    "filename": doc.metadata.get("filename", "")
                })
            
            return {
                "success": True,
                "template_path": template_path,
                "query": query,
                "results_count": len(results),
                "results": results
            }
            
        except Exception as e:
            return {
                "success": False,
                "template_path": template_path,
                "query": query,
                "message": f"Error testing context retrieval: {e}"
            }
    
    def rebuild_category(self, category_path: str) -> bool:
        """
        Rebuild vector store for a specific category
        
        Args:
            category_path: Path to the category to rebuild
            
        Returns:
            True if successful, False otherwise
        """
        try:
            logger.info(f"Rebuilding category: {category_path}")
            self.master_db_manager.rebuild_category(category_path)
            logger.info(f"Successfully rebuilt category: {category_path}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to rebuild category {category_path}: {e}")
            return False
    
    def get_template_categories_mapping(self) -> Dict[str, str]:
        """
        Get mapping of all templates to their categories
        
        Returns:
            Dictionary mapping template paths to categories
        """
        mapping = {}
        
        try:
            # Get all template categories
            template_categories = self.template_manager.get_categories()
            
            for category in template_categories:
                templates = self.template_manager.get_templates(category)
                
                for template in templates:
                    template_path = template["path"]
                    mapped_category = self.master_db_manager.hierarchical_db.get_category_for_template(template_path)
                    mapping[template_path] = mapped_category or "unmapped"
            
            return mapping
            
        except Exception as e:
            logger.error(f"Error creating template mapping: {e}")
            return {}
    
    def validate_system(self) -> Dict[str, Any]:
        """
        Validate the entire vectorization system
        
        Returns:
            Dictionary with validation results
        """
        validation_results = {
            "overall_status": "unknown",
            "issues": [],
            "warnings": [],
            "statistics": {}
        }
        
        try:
            # Check if system is initialized
            status = self.get_system_status()
            if status["status"] != "initialized":
                validation_results["issues"].append("System not properly initialized")
                validation_results["overall_status"] = "failed"
                return validation_results
            
            validation_results["statistics"] = status["statistics"]
            
            # Check template mappings
            template_mapping = self.get_template_categories_mapping()
            unmapped_templates = [path for path, category in template_mapping.items() if category == "unmapped"]
            
            if unmapped_templates:
                validation_results["warnings"].append(f"{len(unmapped_templates)} templates could not be mapped to categories")
            
            # Test a few random context retrievals
            test_queries = ["legal clause", "agreement terms", "notice requirements"]
            successful_retrievals = 0
            
            for template_path in list(template_mapping.keys())[:3]:  # Test first 3 templates
                if template_mapping[template_path] != "unmapped":
                    for query in test_queries[:1]:  # Test with first query
                        result = self.test_context_retrieval(template_path, query, k=1)
                        if result["success"] and result["results_count"] > 0:
                            successful_retrievals += 1
            
            if successful_retrievals == 0:
                validation_results["issues"].append("No successful context retrievals in test")
            elif successful_retrievals < 2:
                validation_results["warnings"].append("Limited successful context retrievals in test")
            
            # Determine overall status
            if validation_results["issues"]:
                validation_results["overall_status"] = "failed"
            elif validation_results["warnings"]:
                validation_results["overall_status"] = "warning"
            else:
                validation_results["overall_status"] = "passed"
            
            return validation_results
            
        except Exception as e:
            validation_results["issues"].append(f"Validation error: {e}")
            validation_results["overall_status"] = "error"
            return validation_results


def main():
    """Main function for command-line usage"""
    import argparse
    
    parser = argparse.ArgumentParser(description="Manage hierarchical vectorization system")
    parser.add_argument("--init", action="store_true", help="Initialize the system")
    parser.add_argument("--force-rebuild", action="store_true", help="Force rebuild all vector stores")
    parser.add_argument("--status", action="store_true", help="Show system status")
    parser.add_argument("--validate", action="store_true", help="Validate the system")
    parser.add_argument("--test-template", type=str, help="Test template mapping for specific template")
    parser.add_argument("--test-query", type=str, default="legal clause", help="Query for testing context retrieval")
    
    args = parser.parse_args()
    
    # Set up logging
    logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
    
    manager = VectorizationManager()
    
    if args.init:
        success = manager.initialize_system(force_rebuild=args.force_rebuild)
        if success:
            print("✓ System initialized successfully")
        else:
            print("✗ System initialization failed")
    
    elif args.status:
        status = manager.get_system_status()
        print(f"Status: {status['status']}")
        print(f"Message: {status['message']}")
        if "statistics" in status:
            stats = status["statistics"]
            print(f"Categories: {stats['total_categories']}")
            print(f"Files: {stats['total_files']}")
    
    elif args.validate:
        results = manager.validate_system()
        print(f"Overall Status: {results['overall_status']}")
        if results["issues"]:
            print("Issues:")
            for issue in results["issues"]:
                print(f"  - {issue}")
        if results["warnings"]:
            print("Warnings:")
            for warning in results["warnings"]:
                print(f"  - {warning}")
    
    elif args.test_template:
        result = manager.test_template_mapping(args.test_template)
        print(f"Template: {result['template_path']}")
        if result["success"]:
            print(f"Mapped Category: {result['mapped_category']}")
            print(f"Vector Store Exists: {result['vector_store_exists']}")
        else:
            print(f"Error: {result['message']}")
        
        # Also test context retrieval
        retrieval_result = manager.test_context_retrieval(args.test_template, args.test_query)
        if retrieval_result["success"]:
            print(f"Context Retrieval: {retrieval_result['results_count']} documents found")
        else:
            print(f"Context Retrieval Error: {retrieval_result['message']}")
    
    else:
        parser.print_help()


if __name__ == "__main__":
    main()
