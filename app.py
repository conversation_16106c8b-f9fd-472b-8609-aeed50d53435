import os
import streamlit as st
from dotenv import load_dotenv
from datetime import datetime
from utils.template_manager import TemplateManager
from database.vector_db import VectorDatabase
from agents.document_manager import LegalDocumentManager
from utils.api_key import get_azure_openai_config
from utils.document_converter import DocumentConverter

load_dotenv()
azure_config = get_azure_openai_config()
template_manager = TemplateManager()
client_db = VectorDatabase("client_files")

# Initialize the agent manager
if "agent_manager" not in st.session_state:
    st.session_state.agent_manager = None

if "step" not in st.session_state:
    st.session_state.step = 1
    st.session_state.selected_category = None
    st.session_state.selected_template = None
    st.session_state.temp_selected_template = None
    st.session_state.preview_template = None
    st.session_state.custom_template = None  # For storing custom uploaded templates
    st.session_state.selected_client_files = []
    st.session_state.extraction_depth = "Standard"
    st.session_state.form_data = {}
    st.session_state.user_instructions = ""
    st.session_state.document = ""
    st.session_state.edited_document = ""
    st.session_state.verification_results = None
    st.session_state.has_critical_issues = False
    st.session_state.revision_history = []

# Set page config
st.set_page_config(
    page_title="Legal Document Generator",
    page_icon="⚖️",
    layout="wide",
    initial_sidebar_state="expanded"
)

# Sidebar
with st.sidebar:
    st.title("⚖️ Legal Document Generator")
    st.markdown("---")

    # Navigation
    if st.button("Start Over", key="start_over"):
        # Reset all session state variables
        st.session_state.step = 1
        st.session_state.selected_category = None
        st.session_state.selected_template = None
        st.session_state.temp_selected_template = None
        st.session_state.preview_template = None
        st.session_state.custom_template = None  # Reset custom template
        st.session_state.selected_client_files = []
        st.session_state.extraction_depth = "Standard"
        st.session_state.form_data = {}
        st.session_state.user_instructions = ""
        st.session_state.document = ""
        st.session_state.edited_document = ""
        st.session_state.verification_results = None
        st.session_state.has_critical_issues = False
        st.session_state.revision_history = []
        st.session_state.agent_manager = None

        st.rerun()

    # Show current step
    st.markdown("### Current Step")
    steps = [
        "1. Select Document Category",
        "2. Select Template",
        "3. Select Client Files",
        "4. Fill Form",
        "5. Verify Document",
        "6. Preview & Download"
    ]

    for i, step in enumerate(steps):
        if i + 1 == st.session_state.step:
            st.markdown(f"**{step}** ←")
        else:
            st.markdown(step)

# Main content
st.title("Legal Document Generator")

# Step 1: Select Document Category
if st.session_state.step == 1:
    st.header("Step 1: Select Document Category")

    # Get categories
    categories = template_manager.get_categories()

    # Display categories as a dropdown
    if categories:
        st.subheader("Select a Document Category")

        # Convert categories to title case for display
        display_categories = [category.title() for category in categories]

        # Create a mapping from display name to actual category name
        category_mapping = {cat.title(): cat for cat in categories}

        # Display category selection dropdown
        selected_category_display = st.selectbox(
            "Choose a document category",
            options=display_categories,
            index=0,
            key="category_dropdown"
        )

        # Get the actual category name from the mapping
        selected_category = category_mapping[selected_category_display]

        # Display category details
        st.write(f"Templates available for {selected_category_display}")

        # Continue button
        if st.button("Continue with this category", key="continue_category"):
            st.session_state.selected_category = selected_category

            # Initialize the agent manager with the selected category
            st.session_state.agent_manager = LegalDocumentManager(category=selected_category)

            st.session_state.step = 2
            st.rerun()
    else:
        st.warning("No document categories found.")

# Step 2: Select Template
elif st.session_state.step == 2:
    st.header(f"Step 2: Select Template - {st.session_state.selected_category.title()}")

    # Create tabs for browsing templates and uploading custom templates
    browse_tab, upload_tab = st.tabs(["Browse Templates", "Upload Custom Template"])

    with browse_tab:
        # Search box
        search_term = st.text_input("Search Templates", "")

        # Get templates
        if search_term:
            templates = template_manager.search_templates(st.session_state.selected_category, search_term)
        else:
            templates = template_manager.get_templates(st.session_state.selected_category)

        # Display templates with options for view type
        if templates:
            # Create a list of template names with file types for the dropdown
            template_names = [f"{template['name']} ({template.get('file_type', '.txt').upper()})" for template in templates]
        else:
            template_names = []

        # Add view type selection (always show this, even if no templates)
        view_type = st.radio("View Type", ["List View", "Grid View"], horizontal=True)

    with upload_tab:
        st.subheader("Upload Your Own Template")
        st.markdown("""
        Upload your own template file to use for document generation.

        **Template Format Guidelines:**
        - Supports text (.txt), Word (.docx), and PDF (.pdf) formats
        - Use square brackets to indicate form fields, e.g., [Name], [Date], [Company]
        - For PDF/DOCX files, ensure text is selectable (not scanned images)
        - Your template will only be available for this session
        """)

        # File uploader for custom template
        uploaded_template = st.file_uploader(
            "Upload Template (.txt, .docx, .pdf)",
            type=["txt", "docx", "pdf"],
            help="Upload a template file to use as a custom template"
        )

        if uploaded_template:
            # Get file extension to determine processing method
            file_ext = os.path.splitext(uploaded_template.name)[1].lower()
            
            try:
                # Read the uploaded template content based on file type
                if file_ext == '.txt':
                    # Text file - decode directly
                    template_content = uploaded_template.getvalue().decode("utf-8")
                elif file_ext in ['.docx', '.pdf']:
                    # Save the uploaded file temporarily to process it
                    import tempfile
                    from utils.document_converter import DocumentConverter
                    
                    with tempfile.NamedTemporaryFile(delete=False, suffix=file_ext) as temp_file:
                        temp_file.write(uploaded_template.getvalue())
                        temp_file.flush()
                        
                        # Extract text using DocumentConverter
                        template_content = DocumentConverter.extract_text_from_file(temp_file.name)
                        
                        # Clean up temp file
                        os.unlink(temp_file.name)
                else:
                    st.error(f"Unsupported file format: {file_ext}")
                    template_content = ""

                if template_content:
                    # Extract fields from the template
                    fields = template_manager.extract_fields(template_content)

                    # Preview the template
                    st.subheader("Template Preview")
                    st.text_area(
                        "Template Content",
                        value=template_content,
                        height=300,
                        key="uploaded_template_preview",
                        disabled=True
                    )

                    # Display extracted fields
                    if fields:
                        st.subheader("Required Fields")
                        field_text = ", ".join([f"[{field}]" for field in fields])
                        st.info(f"This template requires the following fields: {field_text}")
                    else:
                        st.warning("No form fields found in the template. Fields should be indicated with square brackets, e.g., [Name].")

                    # Create a custom template object and store in session state
                    template_name = os.path.splitext(uploaded_template.name)[0].replace("_", " ").title()

                    # Create a temporary path for the session (not actually saved to disk)
                    temp_path = f"session_template_{template_name}"

                    # Create tags from the filename
                    tags = os.path.splitext(uploaded_template.name)[0].split("_")
                    
                    # Get file type for display
                    file_type = file_ext.upper()

                    # Store the custom template in session state
                    custom_template = {
                        "name": f"{template_name} (Custom {file_type})",
                        "path": temp_path,
                        "tags": tags,
                        "content": template_content,  # Store the actual content in the template object
                        "file_type": file_ext,
                        "is_custom": True  # Flag to identify this as a custom template
                    }

                    # Store in session state
                    st.session_state.custom_template = custom_template

                    # Use custom template button
                    if st.button("Use This Custom Template", key="use_custom_template"):
                        st.session_state.selected_template = custom_template
                        st.session_state.step = 3
                        st.rerun()
                else:
                    st.error("Could not extract content from the uploaded file. Please ensure the file contains readable text.")
                    
            except Exception as e:
                st.error(f"Error processing uploaded template: {str(e)}")
                st.info("Please ensure your file is not password-protected and contains readable text.")

    # Display templates section
    if browse_tab.active:
        st.subheader("Select a Template")

        if view_type == "List View":
            # Display template selection dropdown
            selected_template_name = st.selectbox(
                "Choose a template",
                options=template_names,
                index=0,
                key="template_dropdown"
            )

            # Find the selected template
            # Extract just the template name (remove file type suffix)
            selected_base_name = selected_template_name.split(" (")[0]
            selected_template = next((t for t in templates if t["name"] == selected_base_name), None)
        else:  # Grid View
            # Create a grid of template cards
            cols = st.columns(2)

            # Store the selected template in session state if not already present
            if "temp_selected_template" not in st.session_state:
                st.session_state.temp_selected_template = templates[0] if templates else None

            # Display templates in a grid
            for i, template in enumerate(templates):
                with cols[i % 2]:
                    # Create a card-like container for each template
                    with st.container():
                        file_type = template.get('file_type', '.txt').upper()
                        st.markdown(f"### {template['name']} ({file_type})")
                        st.markdown(f"**Tags**: {', '.join(template['tags'])}")

                        # Get a sample of the template content
                        try:
                            template_content = template_manager.get_template_content(template["path"])
                            # Show just the first 100 characters as a preview
                            content_preview = template_content[:100] + "..." if len(template_content) > 100 else template_content
                            st.markdown("**Sample:**")
                            st.text(content_preview)
                        except Exception as e:
                            st.warning(f"Could not load template preview: {e}")

                        # Create two columns for the buttons
                        btn_col1, btn_col2 = st.columns(2)

                        with btn_col1:
                            # Add a select button for each template
                            if st.button(f"Select", key=f"select_template_{i}"):
                                st.session_state.temp_selected_template = template
                                st.rerun()

                        with btn_col2:
                            # Add a quick preview button
                            if st.button(f"Quick Preview", key=f"preview_template_{i}"):
                                # Store the template to preview in session state
                                st.session_state.preview_template = template
                                st.rerun()

                        # Add a visual indicator for the selected template
                        if st.session_state.temp_selected_template and st.session_state.temp_selected_template["path"] == template["path"]:
                            st.success("✓ Selected")

                        st.markdown("---")

            # Display quick preview modal if a template is selected for preview
            if "preview_template" in st.session_state and st.session_state.preview_template:
                # Create a modal-like dialog for the preview
                with st.container():
                    st.markdown("---")
                    st.subheader(f"Quick Preview: {st.session_state.preview_template['name']}")

                    # Get template content
                    preview_content = template_manager.get_template_content(st.session_state.preview_template["path"])

                    # Display template content
                    st.text_area(
                        "Template Content",
                        value=preview_content,
                        height=250,
                        key="quick_preview_content",
                        disabled=True
                    )

                    # Extract and display fields
                    fields = template_manager.extract_fields(preview_content)
                    if fields:
                        st.markdown("**Required Fields:**")
                        field_text = ", ".join([f"[{field}]" for field in fields])
                        st.info(field_text)

                    # Close preview button
                    if st.button("Close Preview", key="close_preview"):
                        # Remove the preview template from session state
                        del st.session_state.preview_template
                        st.rerun()

                    st.markdown("---")

            # Use the template selected in the grid view
            selected_template = st.session_state.temp_selected_template

        if selected_template:
            # Display template details
            st.write(f"Tags: {', '.join(selected_template['tags'])}")

            # Add template preview
            with st.expander("Preview Template", expanded=True):
                # Get template content
                template_content = template_manager.get_template_content(selected_template["path"])

                # Display template content in a text area
                st.text_area(
                    "Template Content",
                    value=template_content,
                    height=300,
                    key="template_preview",
                    disabled=True
                )

                # Extract and display fields from the template
                fields = template_manager.extract_fields(template_content)
                if fields:
                    st.subheader("Required Fields")
                    field_text = ", ".join([f"[{field}]" for field in fields])
                    st.info(f"This template requires the following fields: {field_text}")

            # Continue button
            if st.button("Continue with this template", key="continue_template"):
                st.session_state.selected_template = selected_template
                st.session_state.step = 3
                st.rerun()
        else:
            st.warning("No templates found. Please try a different search term.")

    # Back button
    if st.button("Back to Categories", key="back_to_cat"):
        st.session_state.step = 1
        st.rerun()

# Step 3: Select Client Files (Optional)
elif st.session_state.step == 3:
    st.header("Step 3: Select Client Files (Optional)")

    # Create tabs for different file selection methods
    tab1, tab2 = st.tabs(["Select Existing Files", "Upload New Files"])

    with tab1:
        # Get client files list
        client_files = [f for f in os.listdir("client_files") if f.endswith(('.txt', '.docx', '.pdf'))]

        # Add file filtering options
        col1, col2 = st.columns([3, 1])

        with col1:
            # Search box for filtering files
            search_term = st.text_input("Search Files", "")

        with col2:
            # Sort options
            sort_option = st.selectbox(
                "Sort by",
                ["Name (A-Z)", "Name (Z-A)", "Date (Newest)", "Date (Oldest)"],
                index=0
            )

        # Filter files based on search term
        if search_term:
            client_files = [f for f in client_files if search_term.lower() in f.lower()]

        # Sort files based on selected option
        if sort_option == "Name (A-Z)":
            client_files.sort()
        elif sort_option == "Name (Z-A)":
            client_files.sort(reverse=True)
        elif sort_option == "Date (Newest)" or sort_option == "Date (Oldest)":
            # Get file creation times
            file_times = [(f, os.path.getctime(os.path.join("client_files", f))) for f in client_files]
            # Sort by creation time
            file_times.sort(key=lambda x: x[1], reverse=(sort_option == "Date (Newest)"))
            # Extract sorted file names
            client_files = [f[0] for f in file_times]

        # Display files with checkboxes for multi-selection
        if client_files:
            st.subheader("Select Client Files")

            # Display file selection with checkboxes
            st.write("Select files to use as context for document generation (optional):")

            # Create a container for file selection
            file_container = st.container()

            # Create columns for better layout
            with file_container:
                cols = st.columns(2)
                selected_files = []

                for i, file in enumerate(client_files):
                    with cols[i % 2]:
                        file_stats = os.stat(os.path.join("client_files", file))
                        file_size = file_stats.st_size / 1024  # Size in KB
                        file_date = datetime.fromtimestamp(file_stats.st_ctime).strftime("%Y-%m-%d")

                        # Create a more informative checkbox label
                        checkbox_label = f"{file} ({file_size:.1f} KB, {file_date})"

                        if st.checkbox(checkbox_label, key=f"file_{i}"):
                            selected_files.append(file)

            # Display selected files count and details
            if selected_files:
                st.success(f"Selected {len(selected_files)} file(s)")

                # Show preview of selected files
                with st.expander("Preview Selected Files"):
                    for file in selected_files:
                        file_path = os.path.join("client_files", file)
                        with open(file_path, 'r') as f:
                            content = f.read()
                        st.markdown(f"**{file}**")
                        st.text_area(f"Content of {file}", value=content, height=150, key=f"preview_{file}", disabled=True)

                # Knowledge extraction options
                st.subheader("Knowledge Extraction Options")

                extraction_depth = st.select_slider(
                    "Extraction Depth",
                    options=["Basic", "Standard", "Deep"],
                    value="Standard",
                    help="Basic: Extract only essential information. Standard: Extract context and relationships. Deep: Comprehensive extraction with inferences."
                )

                # Store extraction depth in session state
                st.session_state.extraction_depth = extraction_depth

            # Continue button - note that we don't require file selection
            if st.button("Continue with Selected Files", key="continue_files"):
                if selected_files:
                    file_paths = [os.path.join("client_files", f) for f in selected_files]
                    st.session_state.selected_client_files = file_paths

                    # Automatically vectorize the selected files
                    try:
                        with st.spinner("Vectorizing files... This may take a moment."):
                            # Create a temporary vector database for the selected files
                            temp_db = VectorDatabase("temp_client_files")
                            temp_db.index_files(file_paths)
                            st.success("Files vectorized successfully!")
                    except Exception as e:
                        st.error(f"Error vectorizing files: {e}")

                    # Store extraction depth in session state if not already set
                    if "extraction_depth" not in st.session_state:
                        st.session_state.extraction_depth = "Standard"
                else:
                    st.session_state.selected_client_files = []
                st.session_state.step = 4
                st.rerun()
        else:
            st.warning("No client files found matching your search criteria.")

            # Continue without files button
            if st.button("Continue without client files", key="continue_no_files"):
                st.session_state.selected_client_files = []
                st.session_state.step = 4
                st.rerun()

    with tab2:
        st.subheader("Upload New Client Files")

        # File uploader
        uploaded_files = st.file_uploader(
            "Upload one or more files (.txt, .docx, .pdf)",
            type=["txt", "docx", "pdf"],
            accept_multiple_files=True,
            help="Upload client files to use as context for document generation."
        )

        if uploaded_files:
            st.success(f"Uploaded {len(uploaded_files)} file(s)")

            # Save uploaded files
            saved_files = []
            for uploaded_file in uploaded_files:
                # Create a safe filename
                safe_filename = uploaded_file.name.replace(" ", "_")
                file_path = os.path.join("client_files", safe_filename)

                # Save the file
                with open(file_path, "wb") as f:
                    f.write(uploaded_file.getbuffer())

                saved_files.append(file_path)

            if saved_files:
                st.success(f"Saved {len(saved_files)} file(s) to client_files directory")

                # Continue button
                if st.button("Continue with Uploaded Files", key="continue_uploaded"):
                    st.session_state.selected_client_files = saved_files
                    st.session_state.extraction_depth = "Standard"  # Default extraction depth

                    # Automatically vectorize the uploaded files
                    try:
                        with st.spinner("Vectorizing files... This may take a moment."):
                            # Create a temporary vector database for the uploaded files
                            temp_db = VectorDatabase("temp_client_files")
                            temp_db.index_files(saved_files)
                            st.success("Files vectorized successfully!")
                    except Exception as e:
                        st.error(f"Error vectorizing files: {e}")

                    st.session_state.step = 4
                    st.rerun()
        else:
            st.info("Upload one or more files (.txt, .docx, .pdf) to use as context for document generation.")

    # Back button
    if st.button("Back to Templates", key="back_to_temp"):
        st.session_state.step = 2
        st.rerun()

# Step 4: Fill Form
elif st.session_state.step == 4:
    st.header("Step 4: Fill Form")

    # Show loading spinner while creating form
    with st.spinner("Creating form from template..."):
        # Get extraction depth from session state or use default
        extraction_depth = st.session_state.get("extraction_depth", "Standard")

        # Create empty user_info dictionary (auto-fill disabled)
        user_info = {}

        # Log extraction depth for user feedback if client files were selected
        if st.session_state.selected_client_files:
            st.info(f"Client files will be used as context for document generation (using {extraction_depth} extraction depth)")

        # Create form fields using the agent manager
        form_fields = st.session_state.agent_manager.create_form_fields(
            st.session_state.selected_template["path"],
            user_info
        )

    # Display form
    with st.form("document_form"):
        # Group fields by category
        field_groups = {
            "Personal Information": [],
            "Company Information": [],
            "Contract Details": [],
            "Other Information": []
        }

        # Categorize fields from template only (form_fields is now a list)
        for field_config in form_fields:
            field_name = field_config.get("name", "")
            if any(keyword in field_name.lower() for keyword in ["name", "address", "email", "phone"]):
                field_groups["Personal Information"].append(field_config)
            elif any(keyword in field_name.lower() for keyword in ["company", "employer", "organization"]):
                field_groups["Company Information"].append(field_config)
            elif any(keyword in field_name.lower() for keyword in ["salary", "date", "term", "position"]):
                field_groups["Contract Details"].append(field_config)
            else:
                field_groups["Other Information"].append(field_config)

        # Display fields by group
        for group, fields in field_groups.items():
            if fields:
                st.subheader(group)

                cols = st.columns(2)
                for i, field_config in enumerate(fields):
                    field_name = field_config.get("name", f"field_{i}")
                    field_label = field_config.get("label", field_name.replace("_", " ").title())
                    field_type = field_config.get("type", "text")
                    field_placeholder = field_config.get("placeholder", "")
                    field_required = field_config.get("required", False)

                    with cols[i % 2]:
                        if field_type == "text":
                            text_value = st.text_input(
                                field_label + (" *" if field_required else ""),
                                value=st.session_state.form_data.get(field_name, ""),
                                placeholder=field_placeholder,
                                key=f"form_{field_name}"
                            )
                            st.session_state.form_data[field_name] = text_value
                        elif field_type == "textarea":
                            textarea_value = st.text_area(
                                field_label + (" *" if field_required else ""),
                                value=st.session_state.form_data.get(field_name, ""),
                                placeholder=field_placeholder,
                                key=f"form_{field_name}"
                            )
                            st.session_state.form_data[field_name] = textarea_value
                        elif field_type == "number":
                            # Handle number fields with proper default values
                            validation = field_config.get("validation", {})
                            min_value = validation.get("min", 0)
                            max_value = validation.get("max", None)
                            step = validation.get("step", 1)
                            
                            # Set default value to minimum value if min_value > 0, otherwise 0
                            default_value = min_value if min_value > 0 else 0

                            number_value = st.number_input(
                                field_label + (" *" if field_required else ""),
                                value=st.session_state.form_data.get(field_name, default_value),
                                min_value=min_value,
                                max_value=max_value,
                                step=step,
                                key=f"form_{field_name}"
                            )
                            st.session_state.form_data[field_name] = number_value
                        elif field_type == "date":
                            # Handle date input with proper value initialization
                            from datetime import date
                            
                            # Get existing date value from session state or use today as default
                            existing_date_str = st.session_state.form_data.get(field_name, "")
                            if existing_date_str:
                                try:
                                    # Try to parse existing date string back to date object
                                    from datetime import datetime
                                    existing_date = datetime.strptime(existing_date_str, "%B %d, %Y").date()
                                except:
                                    # If parsing fails, use today
                                    existing_date = date.today()
                            else:
                                existing_date = date.today()
                            
                            date_value = st.date_input(
                                field_label + (" *" if field_required else ""),
                                value=existing_date,
                                key=f"form_{field_name}"
                            )
                            st.session_state.form_data[field_name] = date_value.strftime("%B %d, %Y")
                        elif field_type == "email":
                            email_value = st.text_input(
                                field_label + (" *" if field_required else ""),
                                value=st.session_state.form_data.get(field_name, ""),
                                placeholder=field_placeholder or "<EMAIL>",
                                key=f"form_{field_name}"
                            )
                            st.session_state.form_data[field_name] = email_value
                        elif field_type == "phone":
                            phone_value = st.text_input(
                                field_label + (" *" if field_required else ""),
                                value=st.session_state.form_data.get(field_name, ""),
                                placeholder=field_placeholder or "************",
                                key=f"form_{field_name}"
                            )
                            st.session_state.form_data[field_name] = phone_value
                        else:
                            # Default to text input for unknown types
                            default_value = st.text_input(
                                field_label + (" *" if field_required else ""),
                                value=st.session_state.form_data.get(field_name, ""),
                                placeholder=field_placeholder,
                                key=f"form_{field_name}"
                            )
                            st.session_state.form_data[field_name] = default_value

        # User instructions
        st.subheader("Additional Instructions")
        user_instructions = st.text_area(
            "Provide any specific instructions or clauses to include",
            value=st.session_state.user_instructions,
            height=150,
            key="user_instructions_input"
        )

        # Submit button
        submitted = st.form_submit_button("Generate Document")

        if submitted:
            # Save user instructions to session state
            st.session_state.user_instructions = user_instructions

            # Debug: Check form data before generating document
            print(f"DEBUG APP: Form submitted with data: {st.session_state.form_data}")
            non_empty_fields = {k: v for k, v in st.session_state.form_data.items() if v and str(v).strip()}
            print(f"DEBUG APP: Non-empty fields: {non_empty_fields}")
            
            if not non_empty_fields:
                st.warning("Warning: No form fields were filled. The document will contain only the template structure.")
                st.info("Please fill in the form fields above before generating the document.")
                st.stop()

            # Show loading spinner while generating document
            with st.spinner("Generating document..."):
                # Generate document using the agent manager
                generated_document = st.session_state.agent_manager.generate_document(
                    st.session_state.selected_template["path"],
                    st.session_state.form_data,
                    user_instructions,
                    st.session_state.selected_client_files
                )

                # Debug information
                print(f"Generated document length: {len(generated_document) if generated_document else 0}")

                # Ensure we have a valid document
                if not generated_document or not generated_document.strip():
                    st.error("Failed to generate document. Please try again or adjust your inputs.")
                    # Continue with empty document instead of returning
                    generated_document = "Document generation failed. Please try again."

                # Store the document in session state
                st.session_state.document = generated_document
                st.session_state.edited_document = generated_document

                # Add initial document to revision history
                from datetime import datetime
                timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                st.session_state.revision_history.append({
                    "timestamp": timestamp,
                    "content": generated_document,
                    "type": "Initial Generation"
                })

                # Move to verification step
                st.session_state.step = 5
                st.rerun()

    # Back button
    if st.button("Back to Client Files", key="back_to_client"):
        st.session_state.step = 3
        st.rerun()

# Step 5: Verify Document
elif st.session_state.step == 5:
    st.header("Step 5: Verify Document")

    # Create columns for document preview and verification results
    col1, col2 = st.columns([3, 2])

    with col1:
        # Display document preview with editing capability
        st.subheader("Document Preview (Editable)")

        # Debug information about document content
        print(f"Document in session state: {len(st.session_state.document) if st.session_state.document else 0} chars")

        # Always ensure edited_document is in sync with document
        if "edited_document" not in st.session_state or not st.session_state.edited_document:
            st.session_state.edited_document = st.session_state.document

        print(f"Edited document in session state: {len(st.session_state.edited_document) if st.session_state.edited_document else 0} chars")

        # Debug information to help troubleshoot empty document issues
        if not st.session_state.document and not st.session_state.edited_document:
            st.warning("Document appears to be empty. This may be due to an issue with document generation.")

        # Ensure we have content to display - prioritize document content
        display_content = st.session_state.document
        if not display_content or not display_content.strip():
            display_content = st.session_state.edited_document
        if not display_content or not display_content.strip():
            display_content = "Document content will appear here after generation."

        print(f"Display content length: {len(display_content)}")

        # Editable text area for document preview
        edited_document = st.text_area(
            "",
            value=display_content,
            height=500,
            key="verify_preview",
            disabled=False
        )

        # Save edits button
        if st.button("Save Edits", key="save_edits"):
            # Save the current version to revision history
            from datetime import datetime
            timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            st.session_state.revision_history.append({
                "timestamp": timestamp,
                "content": st.session_state.document,
                "type": "Manual Edit (Verification Stage)"
            })

            # Update the document in session state with edited version
            st.session_state.document = edited_document
            st.session_state.edited_document = edited_document

            # Reset verification results since document has changed
            st.session_state.verification_results = None
            st.session_state.has_critical_issues = False

            st.success("Document edits saved successfully!")
            st.rerun()

    with col2:
        # Verify document if not already verified
        if st.session_state.verification_results is None:
            with st.spinner("Verifying document for correctness and potential issues..."):
                # Verify document using the agent manager
                has_critical_issues, issues = st.session_state.agent_manager.verify_document(
                    st.session_state.document,
                    st.session_state.selected_template["path"],
                    st.session_state.form_data,
                    st.session_state.selected_client_files
                )

                # Store results in session state
                st.session_state.has_critical_issues = has_critical_issues
                st.session_state.verification_results = issues

        # Display verification results
        st.subheader("Verification Results")

        if st.session_state.verification_results:
            # Format verification results for display
            issues = st.session_state.verification_results

            # Group issues by severity
            critical_issues = [i for i in issues if i.get("severity") == "Critical"]
            important_issues = [i for i in issues if i.get("severity") == "Important"]
            minor_issues = [i for i in issues if i.get("severity") == "Minor"]

            # Format results
            result = "# Document Verification Results\n\n"

            # Add critical issues
            if critical_issues:
                result += "## ❌ Critical Issues\n\n"
                for i, issue in enumerate(critical_issues, 1):
                    result += f"### Issue {i}: {issue.get('description')}\n"
                    result += f"**Location**: {issue.get('location')}\n"
                    result += f"**Suggestion**: {issue.get('suggestion')}\n"
                    if issue.get('reference') and issue.get('reference') != "Not specified":
                        result += f"**Reference**: {issue.get('reference')}\n"
                    result += "\n"

            # Add important issues
            if important_issues:
                result += "## ⚠️ Important Issues\n\n"
                for i, issue in enumerate(important_issues, 1):
                    result += f"### Issue {i}: {issue.get('description')}\n"
                    result += f"**Location**: {issue.get('location')}\n"
                    result += f"**Suggestion**: {issue.get('suggestion')}\n"
                    if issue.get('reference') and issue.get('reference') != "Not specified":
                        result += f"**Reference**: {issue.get('reference')}\n"
                    result += "\n"

            # Add minor issues
            if minor_issues:
                result += "## ℹ️ Minor Issues\n\n"
                for i, issue in enumerate(minor_issues, 1):
                    result += f"### Issue {i}: {issue.get('description')}\n"
                    result += f"**Location**: {issue.get('location')}\n"
                    result += f"**Suggestion**: {issue.get('suggestion')}\n"
                    if issue.get('reference') and issue.get('reference') != "Not specified":
                        result += f"**Reference**: {issue.get('reference')}\n"
                    result += "\n"

            st.markdown(result)

            # Warning for critical issues
            if st.session_state.has_critical_issues:
                st.error("⚠️ Critical issues were found in the document. It is recommended to address these issues before proceeding.")

                # Auto-fix option
                if st.button("Auto-Fix Critical Issues", key="auto_fix"):
                    with st.spinner("Automatically fixing critical issues..."):
                        # Extract critical issues
                        critical_issues = [i for i in st.session_state.verification_results if i.get("severity") == "Critical"]

                        # Create instructions for fixing
                        fix_instructions = "Please fix the following critical issues in the document:\n\n"
                        for i, issue in enumerate(critical_issues, 1):
                            fix_instructions += f"{i}. {issue.get('description')} (in {issue.get('location')})\n"
                            fix_instructions += f"   Suggestion: {issue.get('suggestion')}\n\n"

                        # Enhanced fix instructions with context
                        enhanced_instructions = f"""
                        {fix_instructions}

                        When fixing these issues, please refer to the client files and master database for accurate information.
                        Ensure consistency with the client's specific details and requirements.
                        Make sure all legal protections are properly implemented according to standard practices.
                        """

                        # Save current version to revision history
                        from datetime import datetime
                        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                        st.session_state.revision_history.append({
                            "timestamp": timestamp,
                            "content": st.session_state.document,
                            "type": "Before Auto-Fix Critical Issues"
                        })

                        # First get the current document content as a template
                        current_doc = st.session_state.document

                        # Use the agent manager to revise the document
                        st.session_state.document = st.session_state.agent_manager.revise_document(
                            current_doc,
                            enhanced_instructions
                        )
                        st.session_state.edited_document = st.session_state.document

                        # Reset verification results
                        st.session_state.verification_results = None
                        st.session_state.has_critical_issues = False

                        st.rerun()
        else:
            st.success("✅ No issues found. The document appears to be correct and consistent.")

        # Re-verify button
        if st.button("Re-Verify Document", key="reverify"):
            st.session_state.verification_results = None
            st.rerun()



    # Options for proceeding
    st.markdown("---")
    col1, col2, col3 = st.columns(3)

    with col1:
        if st.button("Back to Form for Changes", key="back_to_form_from_verify"):
            st.session_state.step = 4
            st.rerun()

    with col2:
        # Conditional button based on critical issues
        if st.session_state.has_critical_issues:
            if st.button("Proceed Anyway (Not Recommended)", key="proceed_anyway"):
                st.session_state.step = 6
                st.rerun()
        else:
            if st.button("Proceed to Download", key="proceed_to_download"):
                st.session_state.step = 6
                st.rerun()

    with col3:
        if st.button("Make Quick Revisions", key="quick_revisions"):
            st.session_state.step = 6
            st.rerun()

# Step 6: Preview & Download
elif st.session_state.step == 6:
    st.header("Step 6: Preview & Download")

    # Display document preview with editing capability
    st.subheader("Document Preview (Editable)")

    # Debug information about document content
    print(f"Step 6 - Document in session state: {len(st.session_state.document) if st.session_state.document else 0} chars")

    # Always ensure edited_document is in sync with document
    if "edited_document" not in st.session_state or not st.session_state.edited_document:
        st.session_state.edited_document = st.session_state.document

    print(f"Step 6 - Edited document in session state: {len(st.session_state.edited_document) if st.session_state.edited_document else 0} chars")

    # Debug information to help troubleshoot empty document issues
    if not st.session_state.document and not st.session_state.edited_document:
        st.warning("Document appears to be empty. This may be due to an issue with document generation.")

    # Ensure we have content to display - prioritize document content
    display_content = st.session_state.document
    if not display_content or not display_content.strip():
        display_content = st.session_state.edited_document
    if not display_content or not display_content.strip():
        display_content = "Document content will appear here after generation."

    print(f"Step 6 - Display content length: {len(display_content)}")

    # Editable text area for document preview
    edited_document = st.text_area(
        "",
        value=display_content,
        height=400,
        key="preview",
        disabled=False
    )

    # Save edits button
    col1, col2 = st.columns([1, 3])
    with col1:
        if st.button("Save Edits", key="save_edits_preview"):
            # Save the current version to revision history
            from datetime import datetime
            timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            st.session_state.revision_history.append({
                "timestamp": timestamp,
                "content": st.session_state.document,
                "type": "Manual Edit (Preview Stage)"
            })

            # Update the document in session state with edited version
            st.session_state.document = edited_document
            st.session_state.edited_document = edited_document

            # Reset verification results since document has changed
            st.session_state.verification_results = None
            st.session_state.has_critical_issues = False

            st.success("Document edits saved successfully!")

    # Create tabs for different revision options
    tab1, tab2, tab3, tab4 = st.tabs(["Quick Revisions", "Advanced Revisions", "Revision History", "Download"])

    with tab1:
        st.subheader("Quick Revisions")

        # Common revision options
        col1, col2 = st.columns(2)

        with col1:
            if st.button("Improve Language & Clarity", key="improve_language"):
                with st.spinner("Improving document language and clarity..."):
                    # Save current version to revision history
                    from datetime import datetime
                    timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                    st.session_state.revision_history.append({
                        "timestamp": timestamp,
                        "content": st.session_state.document,
                        "type": "Before Language Improvement"
                    })

                    # Generate revised document
                    st.session_state.document = st.session_state.agent_manager.revise_document(
                        st.session_state.document,
                        "Improve the language and clarity of the document. Make it more concise and professional. Ensure all legal terms are used correctly."
                    )
                    st.session_state.edited_document = st.session_state.document
                    st.session_state.verification_results = None
                    st.session_state.has_critical_issues = False
                    st.rerun()

        with col2:
            if st.button("Add More Detail", key="add_detail"):
                with st.spinner("Adding more detail to the document..."):
                    # Save current version to revision history
                    from datetime import datetime
                    timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                    st.session_state.revision_history.append({
                        "timestamp": timestamp,
                        "content": st.session_state.document,
                        "type": "Before Adding Detail"
                    })

                    # Generate revised document
                    st.session_state.document = st.session_state.agent_manager.revise_document(
                        st.session_state.document,
                        "Add more detail to the document. Expand on important sections and provide more specific information where possible."
                    )
                    st.session_state.edited_document = st.session_state.document
                    st.session_state.verification_results = None
                    st.session_state.has_critical_issues = False
                    st.rerun()

        col1, col2 = st.columns(2)

        with col1:
            if st.button("Simplify Language", key="simplify"):
                with st.spinner("Simplifying document language..."):
                    # Save current version to revision history
                    from datetime import datetime
                    timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                    st.session_state.revision_history.append({
                        "timestamp": timestamp,
                        "content": st.session_state.document,
                        "type": "Before Language Simplification"
                    })

                    # Generate revised document
                    st.session_state.document = st.session_state.agent_manager.revise_document(
                        st.session_state.document,
                        "Simplify the language of the document to make it more accessible to non-legal readers while maintaining its legal validity."
                    )
                    st.session_state.edited_document = st.session_state.document
                    st.session_state.verification_results = None
                    st.session_state.has_critical_issues = False
                    st.rerun()

        with col2:
            if st.button("Strengthen Legal Protections", key="strengthen"):
                with st.spinner("Strengthening legal protections..."):
                    # Save current version to revision history
                    from datetime import datetime
                    timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                    st.session_state.revision_history.append({
                        "timestamp": timestamp,
                        "content": st.session_state.document,
                        "type": "Before Strengthening Protections"
                    })

                    # Generate revised document
                    st.session_state.document = st.session_state.agent_manager.revise_document(
                        st.session_state.document,
                        "Strengthen the legal protections in this document. Add appropriate clauses and language to better protect the client's interests."
                    )
                    st.session_state.edited_document = st.session_state.document
                    st.session_state.verification_results = None
                    st.session_state.has_critical_issues = False
                    st.rerun()

    with tab2:
        st.subheader("Advanced Revisions")

        # Revision form
        with st.form("revision_form"):
            revision_instructions = st.text_area(
                "Provide detailed instructions for revising the document",
                height=150,
                key="revision_instructions",
                placeholder="Example: Add a confidentiality clause, change the payment terms to net-30, clarify the termination conditions..."
            )

            # Revision options
            col1, col2 = st.columns(2)

            with col1:
                preserve_structure = st.checkbox("Preserve document structure", value=True,
                                               help="Keep the overall structure of the document intact")

            with col2:
                focus_on_legal = st.checkbox("Focus on legal accuracy", value=True,
                                           help="Prioritize legal accuracy and compliance in revisions")

            # Submit button
            submitted = st.form_submit_button("Revise Document")

            if submitted and revision_instructions:
                # Show loading spinner while revising document
                with st.spinner("Revising document..."):
                    # Save current version to revision history
                    from datetime import datetime
                    timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                    st.session_state.revision_history.append({
                        "timestamp": timestamp,
                        "content": st.session_state.document,
                        "type": "Before Advanced Revision"
                    })

                    # Add options to the revision instructions
                    full_instructions = revision_instructions
                    if preserve_structure:
                        full_instructions += "\n\nPlease preserve the overall structure of the document."
                    if focus_on_legal:
                        full_instructions += "\n\nFocus on legal accuracy and compliance in your revisions."

                    # Revise document using the agent manager
                    st.session_state.document = st.session_state.agent_manager.revise_document(
                        st.session_state.document,
                        full_instructions
                    )
                    st.session_state.edited_document = st.session_state.document

                    # Reset verification results since document has changed
                    st.session_state.verification_results = None
                    st.session_state.has_critical_issues = False

                    # Go back to verification step
                    st.session_state.step = 5
                    st.rerun()

    with tab3:
        st.subheader("Revision History")

        if not st.session_state.revision_history:
            st.info("No revision history available. Changes to the document will be tracked here.")
        else:
            # Display revision history in reverse chronological order (newest first)
            for i, revision in enumerate(reversed(st.session_state.revision_history)):
                with st.expander(f"Revision {len(st.session_state.revision_history) - i}: {revision['timestamp']} - {revision['type']}"):
                    # Show the content of this revision
                    st.text_area(
                        "Content",
                        value=revision['content'],
                        height=200,
                        key=f"revision_{i}",
                        disabled=True
                    )

                    # Add a button to restore this revision
                    if st.button("Restore This Version", key=f"restore_{i}"):
                        # Update the document with this revision
                        st.session_state.document = revision['content']
                        st.session_state.edited_document = revision['content']

                        # Add a new entry to revision history
                        from datetime import datetime
                        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                        st.session_state.revision_history.append({
                            "timestamp": timestamp,
                            "content": revision['content'],
                            "type": "Restored from History"
                        })

                        # Reset verification results
                        st.session_state.verification_results = None
                        st.session_state.has_critical_issues = False

                        st.success("Document restored to selected version!")
                        st.rerun()

    with tab4:
        st.subheader("Download Options")

        # Format options
        format_options = ["Text (.txt)", "Word (.docx)", "PDF (.pdf)"]
        selected_format = st.radio("Select format", format_options, index=0)

        # Download button
        if selected_format == "Text (.txt)":
            st.download_button(
                label="Download Document as Text",
                data=st.session_state.document,
                file_name=f"{st.session_state.selected_template['name']}_document.txt",
                mime="text/plain"
            )
        elif selected_format == "Word (.docx)":
            try:
                docx_data = DocumentConverter.text_to_docx(st.session_state.document)
                st.download_button(
                    label="Download Document as Word",
                    data=docx_data,
                    file_name=f"{st.session_state.selected_template['name']}_document.docx",
                    mime="application/vnd.openxmlformats-officedocument.wordprocessingml.document"
                )
            except Exception as e:
                st.error(f"Error creating Word document: {e}")
                st.download_button(
                    label="Download as Text Instead",
                    data=st.session_state.document,
                    file_name=f"{st.session_state.selected_template['name']}_document.txt",
                    mime="text/plain"
                )
        elif selected_format == "PDF (.pdf)":
            try:
                pdf_data = DocumentConverter.text_to_pdf(st.session_state.document)
                st.download_button(
                    label="Download Document as PDF",
                    data=pdf_data,
                    file_name=f"{st.session_state.selected_template['name']}_document.pdf",
                    mime="application/pdf"
                )
            except Exception as e:
                st.error(f"Error creating PDF document: {e}")
                st.download_button(
                    label="Download as Text Instead",
                    data=st.session_state.document,
                    file_name=f"{st.session_state.selected_template['name']}_document.txt",
                    mime="text/plain"
                )

    # Back button
    if st.button("Back to Verification", key="back_to_verify"):
        st.session_state.step = 5
        st.rerun()

# Initialize client files database if it doesn't exist
try:
    if not os.path.exists("database/client_files"):
        with st.spinner("Initializing client files database..."):
            client_db.index_directory("client_files")
        st.success("Client files database initialized successfully!")
except Exception as e:
    st.error(f"Error initializing vector database: {e}")

