import os
import uuid
import shutil
from crewai import Agent, Task
from langchain_openai import AzureChatOpenAI
from database.vector_db import VectorDatabase
from utils.document_processor import DocumentProcessor
from utils.api_key import get_azure_openai_config
from typing import Dict, List, Any, Optional, Tuple

class UserDataAgent:
    def __init__(self):
        """
        Initialize the user data agent

        This agent works with user-selected client files to:
        1. Extract personal details for form auto-filling
        2. Extract relevant clauses and information for document drafting
        3. Create a temporary vector database for the current session only
           (unlike master_db which is vectorized once and persists)
        """
        azure_config = get_azure_openai_config()
        self.llm = AzureChatOpenAI(
            deployment_name=azure_config['deployment_name'],
            api_key=azure_config['api_key'],
            azure_endpoint=azure_config['endpoint'],
            api_version=azure_config['api_version']
        )
        # Generate a unique session ID for this instance
        self.session_id = f"session_{uuid.uuid4().hex[:8]}"
        self.client_db = None
        self.temp_db_path = f"database/temp_client_files_{self.session_id}"

        # Ensure the temporary database directory exists
        os.makedirs(self.temp_db_path, exist_ok=True)

    def create_agent(self) -> Agent:
        """
        Create the CrewAI agent

        Returns:
            CrewAI Agent
        """
        return Agent(
            role="User Data Extraction Specialist",
            goal="Extract accurate user information and relevant clauses from client documents",
            backstory=(
                "You are an expert in extracting personal and professional information "
                "from legal documents. You have a keen eye for detail and can identify "
                "key information even when it's not explicitly stated. You also excel at "
                "identifying relevant legal clauses and contextual information that can "
                "be used to enhance document drafting."
            ),
            verbose=True,
            llm=self.llm,
            allow_delegation=False
        )

    def create_task(self, client_name: str) -> Task:
        """
        Create a task for the agent

        Args:
            client_name: Name of the client

        Returns:
            CrewAI Task
        """
        return Task(
            description=(
                f"Extract all relevant information for client '{client_name}' from their documents. "
                f"This includes:\n"
                f"1. Personal details: name, address, contact information, etc.\n"
                f"2. Professional information: employment, position, company details, etc.\n"
                f"3. Relevant legal clauses and contextual information that could be useful for drafting.\n"
                f"4. Any specific requirements or preferences mentioned in the documents."
            ),
            expected_output=(
                "1. A comprehensive dictionary of user information for form auto-filling.\n"
                "2. A collection of relevant clauses and contextual information for document drafting."
            ),
            agent=self.create_agent()
        )

    def extract_user_info(self, file_paths: List[str], extraction_depth: str = "Standard") -> Dict[str, str]:
        """
        Extract user information from client documents

        Args:
            file_paths: List of paths to client files
            extraction_depth: Depth of extraction ("Basic", "Standard", or "Deep")

        Returns:
            Dictionary of user information
        """
        # If no files provided, return empty dictionary
        if not file_paths:
            return {"name": "Client"}

        # Create a temporary vector database for the selected files using the session-specific path
        self.client_db = VectorDatabase(f"temp_client_files_{self.session_id}")

        # Index the selected files
        self.client_db.index_files(file_paths)

        # Adjust query based on extraction depth
        if extraction_depth == "Basic":
            client_query = "Extract only essential personal information including name, address, contact details"
            k_multiplier = 1  # Fewer results for basic extraction
        elif extraction_depth == "Deep":
            client_query = "Extract comprehensive personal and professional information including name, address, contact details, employment information, relationships, history, preferences, and any other relevant details"
            k_multiplier = 3  # More results for deep extraction
        else:  # Standard
            client_query = "Extract personal and professional information including name, address, contact details, employment information, and key context"
            k_multiplier = 2  # Standard number of results

        # Search for relevant information in the indexed files
        documents = self.client_db.search(client_query, k=len(file_paths) * k_multiplier)

        # If no results from vector search, read files directly
        if not documents:
            documents = []
            for file_path in file_paths:
                try:
                    with open(file_path, 'r') as f:
                        content = f.read()
                        file_name = os.path.basename(file_path)
                        documents.append({
                            "content": content,
                            "metadata": {"source": file_path}
                        })
                except Exception as e:
                    print(f"Error reading {file_path}: {e}")

        # Extract user information from documents
        user_info = DocumentProcessor.extract_user_info(documents)

        # Try to extract a name if not found
        if "name" not in user_info:
            # Try to extract name from filenames
            for file_path in file_paths:
                file_name = os.path.basename(file_path)
                # Look for common name patterns in filenames
                name_match = None
                for part in file_name.replace('.txt', '').split('_'):
                    if part[0].isupper() and len(part) > 2:
                        name_match = part
                        break
                if name_match:
                    user_info["name"] = name_match
                    break

            # If still no name, use a generic one
            if "name" not in user_info:
                user_info["name"] = "Client"

        # Extract information for template fields from documents using LLM
        if documents:
            # Create a prompt to extract information that matches template fields
            extraction_prompt = f"""
            I need to extract specific information from these client documents that can be used to fill in a legal document template.
            Please analyze the content and extract information for common template fields.

            Focus on extracting information for these common fields:
            1. Client name, full name, or party name
            2. Address, location, or residence
            3. Phone number or contact information
            4. Email address
            5. Company or organization name
            6. Position or job title
            7. Dates (agreement date, start date, etc.)
            8. Amounts, fees, or financial terms
            9. Duration or term periods
            10. Any other specific details that would be useful in a legal document

            Also identify all people mentioned in the documents with their roles and relationships.
            For each person, extract:
            - Full name
            - Role or relationship to the client
            - Contact information if available
            - Any other relevant details about this person

            Format the response as a clean dictionary with appropriate keys and values.
            Use simple, clear keys like 'name', 'address', 'phone', 'email', etc.
            For additional people, use keys like 'person1_name', 'person1_role', 'person2_name', etc.
            Do NOT use 'context_' prefix for any keys.
            """

            # Adjust based on extraction depth
            if extraction_depth == "Deep":
                extraction_prompt += """
                Be very thorough and extract as much detailed information as possible.
                Look for implied information and make reasonable inferences where appropriate.
                Extract information about all parties mentioned, even if they're only referenced indirectly.
                Identify relationships between different people mentioned in the documents.
                """
            elif extraction_depth == "Basic":
                extraction_prompt += """
                Focus only on the most essential information.
                Keep it concise and only extract clearly stated information.
                Only extract information about the main parties directly involved.
                """

            # Prepare document content for the prompt
            doc_content = "\n\n".join([f"Document: {doc['metadata'].get('source', 'Unknown')}\nContent: {doc['content'][:1000]}..." for doc in documents[:3]])
            full_prompt = f"{extraction_prompt}\n\nDocuments:\n{doc_content}"

            try:
                # Get extracted information using LLM
                response = self.llm.invoke(full_prompt)
                extracted_info = response.content

                # Try to parse the response as a dictionary-like structure
                import re
                info_dict = {}

                # Look for key-value pairs in the format "key: value" or "key - value"
                pattern = r'["\']?([a-zA-Z_]+)["\']?\s*[:|-]\s*["\']?(.*?)["\']?(?:,|\n|$)'
                matches = re.findall(pattern, extracted_info)

                for key, value in matches:
                    if key and value and key.strip() and value.strip():
                        info_dict[key.strip()] = value.strip()

                # Add the extracted information to user_info
                for key, value in info_dict.items():
                    user_info[key] = value

                # If no structured data was extracted, try to extract basic information
                if not info_dict:
                    # Try to extract name
                    name_pattern = r'(?:name|client|customer)[\s:]+([A-Z][a-z]+ [A-Z][a-z]+)'
                    name_match = re.search(name_pattern, extracted_info, re.IGNORECASE)
                    if name_match:
                        user_info["name"] = name_match.group(1)

                    # Try to extract email
                    email_pattern = r'([a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,})'
                    email_match = re.search(email_pattern, extracted_info)
                    if email_match:
                        user_info["email"] = email_match.group(1)

                    # Try to extract phone
                    phone_pattern = r'(\(\d{3}\)\s*\d{3}-\d{4}|\d{3}[-.\s]\d{3}[-.\s]\d{4})'
                    phone_match = re.search(phone_pattern, extracted_info)
                    if phone_match:
                        user_info["phone"] = phone_match.group(1)

            except Exception as e:
                print(f"Error extracting information: {e}")

        return user_info

    def extract_relevant_clauses(self, file_paths: List[str], document_type: str, template_name: str) -> List[Dict[str, Any]]:
        """
        Extract relevant clauses and information from client files to use as a knowledge base for document drafting

        Args:
            file_paths: List of paths to client files
            document_type: Type of document being drafted (e.g., "agreement", "contract")
            template_name: Name of the template being used

        Returns:
            List of relevant clauses and information
        """
        # If no files provided, return empty list
        if not file_paths:
            return []

        # Use the existing client_db if it exists, otherwise create a new one
        if not self.client_db:
            self.client_db = VectorDatabase(f"temp_client_files_{self.session_id}")
            self.client_db.index_files(file_paths)

        # Create queries based on document type and template
        queries = [
            f"Extract clauses related to {document_type}",
            f"Find information relevant to {template_name}",
            "Extract legal terms and conditions",
            "Find specific requirements or preferences",
            "Extract information about obligations and responsibilities"
        ]

        # Search for relevant clauses in the indexed files
        all_clauses = []
        for query in queries:
            results = self.client_db.search(query, k=3)  # Get top 3 results per query
            all_clauses.extend(results)

        # Remove duplicates based on content
        unique_clauses = []
        seen_content = set()

        for clause in all_clauses:
            # Create a simplified version of the content for comparison
            simplified = ' '.join(clause['content'].lower().split())

            # Only add if we haven't seen this content before
            if simplified not in seen_content:
                seen_content.add(simplified)
                unique_clauses.append(clause)

        return unique_clauses

    def cleanup_session(self):
        """
        Clean up temporary session data

        This should be called when the session is complete to remove temporary files
        """
        # Remove the temporary database directory if it exists
        if os.path.exists(self.temp_db_path):
            try:
                shutil.rmtree(self.temp_db_path)
                print(f"Cleaned up temporary database at {self.temp_db_path}")
            except Exception as e:
                print(f"Error cleaning up temporary database: {e}")
