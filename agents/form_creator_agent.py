import re
import os
import time
from crewai import Agent, Task
from langchain_openai import AzureChatOpenAI
from utils.template_manager import TemplateManager
from utils.api_key import get_azure_openai_config
from typing import Dict, List, Any, Tuple, Optional
import json
import logging

logger = logging.getLogger(__name__)

class FormCreatorAgent:
    def __init__(self):
        """
        Initialize the form creator agent

        This agent intelligently creates dynamic forms based on different templates.
        It analyzes the template structure, determines appropriate field types,
        and pre-fills values from user information when available.

        The agent is designed to be generic and work with any type of template,
        regardless of the document category or purpose.
        """
        azure_config = get_azure_openai_config()
        self.llm = AzureChatOpenAI(
            deployment_name=azure_config['deployment_name'],
            api_key=azure_config['api_key'],
            azure_endpoint=azure_config['endpoint'],
            api_version=azure_config['api_version']
        )
        self.template_manager = TemplateManager()

    def create_agent(self) -> Agent:
        """
        Create the CrewAI agent

        Returns:
            CrewAI Agent
        """
        return Agent(
            role="Form Design Specialist",
            goal="Create intuitive and comprehensive forms for legal document generation",
            backstory=(
                "You are an expert in form design and user experience. You understand what information "
                "is needed for different types of legal documents and can create forms that are easy to "
                "use while ensuring all necessary information is collected. You can analyze templates "
                "to determine the appropriate form fields and their relationships."
            ),
            verbose=True,
            llm=self.llm,
            allow_delegation=False
        )

    def create_task(self, template_path: str, user_info: Dict[str, str]) -> Task:
        """
        Create a task for the agent

        Args:
            template_path: Path to the template file
            user_info: Dictionary of user information

        Returns:
            CrewAI Task
        """
        # Get template name from path for more context
        template_name = os.path.basename(template_path).replace("_template.txt", "").replace("_", " ").title()

        return Task(
            description=(
                f"Create a dynamic form for the '{template_name}' template. "
                f"The form should be pre-filled with the following user information: {user_info}. "
                f"Identify all fields in the template that need to be filled and create appropriate form fields "
                f"for each one. Group related fields together and provide clear labels and instructions. "
                f"Determine the most appropriate field types based on the expected content and context."
            ),
            expected_output=(
                "A dictionary mapping field names to their form configurations, including field type, "
                "label, default value (if available from user_info), and any validation requirements."
            ),
            agent=self.create_agent()
        )

    def create_form_fields(self, template_content: str) -> List[Dict[str, Any]]:
        """Create form fields from template content using improved LLM analysis."""
        try:
            # First, extract all potential placeholders using multiple patterns
            placeholders = self._extract_placeholders(template_content)

            if not placeholders:
                logger.warning("No placeholders found in template")
                return []

            # Create a more robust prompt for LLM analysis
            prompt = self._create_enhanced_prompt(template_content, placeholders)

            # Get response from LLM with retry logic
            response = self._get_llm_response_with_retry(prompt)

            if not response:
                logger.error("Failed to get valid response from LLM")
                return self._create_fallback_fields(placeholders)

            # Parse and validate the response
            form_fields = self._parse_llm_response(response)

            if not form_fields:
                logger.warning("LLM response parsing failed, using fallback")
                return self._create_fallback_fields(placeholders)

            # Enhance fields with additional metadata
            enhanced_fields = self._enhance_form_fields(form_fields, template_content)

            logger.info(f"Successfully created {len(enhanced_fields)} form fields")
            return enhanced_fields

        except Exception as e:
            logger.error(f"Error creating form fields: {e}")
            # Return fallback fields based on simple placeholder extraction
            placeholders = self._extract_placeholders(template_content)
            return self._create_fallback_fields(placeholders)

    def _extract_placeholders(self, template_content: str) -> List[str]:
        """Extract placeholders using multiple patterns."""
        placeholders = set()

        # Pattern 1: Square brackets [field_name]
        bracket_pattern = r'\[([^\]]+)\]'
        bracket_matches = re.findall(bracket_pattern, template_content)
        placeholders.update(bracket_matches)

        # Pattern 2: Underscores _____ (convert to generic field names)
        underscore_pattern = r'_{3,}'
        underscore_matches = re.findall(underscore_pattern, template_content)
        for i, match in enumerate(underscore_matches):
            placeholders.add(f"underscore_field_{i+1}")

        # Pattern 3: Curly braces {field_name}
        brace_pattern = r'\{([^}]+)\}'
        brace_matches = re.findall(brace_pattern, template_content)
        placeholders.update(brace_matches)

        # Pattern 4: Double parentheses ((field_name))
        paren_pattern = r'\(\(([^)]+)\)\)'
        paren_matches = re.findall(paren_pattern, template_content)
        placeholders.update(paren_matches)

        # Pattern 5: Dotted placeholders (…………………) - common in legal documents
        dot_pattern = r'…{3,}'
        dot_matches = re.findall(dot_pattern, template_content)
        
        # Create meaningful field names based on context for dotted placeholders
        dotted_placeholders = self._create_contextual_field_names(template_content, len(dot_matches))
        placeholders.update(dotted_placeholders)

        # Clean and filter placeholders
        cleaned_placeholders = []
        for placeholder in placeholders:
            # Remove extra whitespace and convert to lowercase
            cleaned = placeholder.strip().lower()
            # Skip very short placeholders but allow longer ones for contextual names
            if len(cleaned) >= 2:
                cleaned_placeholders.append(cleaned)

        logger.info(f"Extracted {len(cleaned_placeholders)} placeholders: {cleaned_placeholders}")
        return cleaned_placeholders

    def _create_enhanced_prompt(self, template_content: str, placeholders: List[str]) -> str:
        """Create an enhanced prompt for LLM analysis."""
        return f"""You are a form design expert. Analyze this legal document template and create appropriate form fields.

TEMPLATE CONTENT:
{template_content[:2000]}...

DETECTED PLACEHOLDERS:
{', '.join(placeholders)}

INSTRUCTIONS:
1. For each placeholder, determine the most appropriate form field type
2. Create user-friendly labels (convert snake_case to Title Case)
3. Determine if the field should be required based on legal document context
4. Add appropriate validation rules where needed

FIELD TYPES TO USE:
- text: Short text inputs (names, titles, IDs)
- textarea: Long text (addresses, descriptions, clauses)
- date: Any date-related fields
- number: Numeric values (amounts, quantities)
- email: Email addresses
- phone: Phone numbers
- select: If you can determine specific options

RETURN FORMAT:
Return ONLY a valid JSON array with this exact structure:
[
  {{
    "name": "field_name",
    "type": "field_type",
    "label": "Human Readable Label",
    "required": true/false,
    "placeholder": "helpful placeholder text",
    "validation": {{"pattern": "regex_if_needed", "min": number_if_needed}}
  }}
]

Do not include any explanation or markdown formatting. Return only the JSON array."""

    def _get_llm_response_with_retry(self, prompt: str, max_retries: int = 3) -> Optional[str]:
        """Get LLM response with retry logic."""
        for attempt in range(max_retries):
            try:
                # Use invoke method instead of generate
                response = self.llm.invoke(prompt)

                # Extract content from response
                if hasattr(response, 'content'):
                    return response.content.strip()
                else:
                    return str(response).strip()

            except Exception as e:
                logger.warning(f"LLM request attempt {attempt + 1} failed: {e}")
                if attempt < max_retries - 1:
                    time.sleep(2 ** attempt)  # Exponential backoff
                else:
                    logger.error(f"All LLM request attempts failed")

        return None

    def _parse_llm_response(self, response: str) -> List[Dict[str, Any]]:
        """Parse LLM response with robust error handling."""
        try:
            # Clean the response
            json_str = response.strip()

            # Remove markdown formatting
            if json_str.startswith('```json'):
                json_str = json_str[7:]
            if json_str.startswith('```'):
                json_str = json_str[3:]
            if json_str.endswith('```'):
                json_str = json_str[:-3]

            json_str = json_str.strip()

            # Try to find JSON array in the response
            start_idx = json_str.find('[')
            end_idx = json_str.rfind(']')

            if start_idx != -1 and end_idx != -1 and end_idx > start_idx:
                json_str = json_str[start_idx:end_idx + 1]

            # Parse JSON
            form_fields = json.loads(json_str)

            # Validate structure
            if not isinstance(form_fields, list):
                logger.error("Response is not a list")
                return []

            validated_fields = []
            for field in form_fields:
                if self._validate_field_structure(field):
                    validated_fields.append(field)
                else:
                    logger.warning(f"Invalid field structure: {field}")

            return validated_fields

        except json.JSONDecodeError as e:
            logger.error(f"JSON parsing failed: {e}")
            logger.debug(f"Response content: {response[:500]}")
            return []
        except Exception as e:
            logger.error(f"Error parsing LLM response: {e}")
            return []

    def _validate_field_structure(self, field: Dict[str, Any]) -> bool:
        """Validate that a field has the required structure."""
        required_keys = ['name', 'type', 'label']
        return (isinstance(field, dict) and
                all(key in field for key in required_keys) and
                all(isinstance(field[key], str) for key in required_keys) and
                field['type'] in ['text', 'textarea', 'date', 'number', 'email', 'phone', 'select'])

    def _create_fallback_fields(self, placeholders: List[str]) -> List[Dict[str, Any]]:
        """Create fallback form fields when LLM analysis fails."""
        fallback_fields = []

        for placeholder in placeholders:
            # Determine field type based on placeholder name
            field_type, validation = self._determine_field_type(placeholder)

            # Create user-friendly label
            label = self._create_field_label(placeholder)

            # Create placeholder text
            placeholder_text = self._create_placeholder(placeholder, field_type)

            field = {
                "name": placeholder,
                "type": field_type,
                "label": label,
                "required": True,  # Default to required for legal documents
                "placeholder": placeholder_text
            }

            if validation:
                field["validation"] = validation

            fallback_fields.append(field)

        logger.info(f"Created {len(fallback_fields)} fallback form fields")
        return fallback_fields

    def _enhance_form_fields(self, form_fields: List[Dict[str, Any]], template_content: str) -> List[Dict[str, Any]]:
        """Enhance form fields with additional metadata and validation."""
        enhanced_fields = []

        for field in form_fields:
            enhanced_field = field.copy()

            # Add default placeholder if not present
            if "placeholder" not in enhanced_field:
                enhanced_field["placeholder"] = self._create_placeholder(
                    enhanced_field["name"], enhanced_field["type"]
                )

            # Add default required status if not present
            if "required" not in enhanced_field:
                enhanced_field["required"] = True  # Default to required for legal documents

            # Enhance validation based on field type
            if enhanced_field["type"] == "email" and "validation" not in enhanced_field:
                enhanced_field["validation"] = {"pattern": r"[^@]+@[^@]+\.[^@]+"}
            elif enhanced_field["type"] == "phone" and "validation" not in enhanced_field:
                enhanced_field["validation"] = {"pattern": r"[\d\-\(\)\+\s]+"}
            elif enhanced_field["type"] == "number" and "validation" not in enhanced_field:
                enhanced_field["validation"] = {"min": 0}

            # Add help text for complex fields
            field_name_lower = enhanced_field["name"].lower()
            if any(term in field_name_lower for term in ["date", "deadline", "expiry"]):
                enhanced_field["help_text"] = "Select the appropriate date from the calendar"
            elif any(term in field_name_lower for term in ["amount", "payment", "salary", "fee"]):
                enhanced_field["help_text"] = "Enter the amount in numbers only"
            elif any(term in field_name_lower for term in ["address", "location"]):
                enhanced_field["help_text"] = "Enter the complete address including city and postal code"

            enhanced_fields.append(enhanced_field)

        return enhanced_fields

    def _analyze_template_context(self, template_content: str, template_name: str) -> Dict[str, Any]:
        """
        Analyze the template content to understand the context and relationships between fields

        This method is generic and works with any type of template.

        Args:
            template_content: The content of the template
            template_name: The name of the template

        Returns:
            Dictionary with context information
        """
        context = {
            "template_name": template_name,
            "field_relationships": {},
            "sections": [],
            "has_dates": "[date" in template_content.lower() or any(date_term in template_content.lower() for term in ["date", "day", "month", "year"] for date_term in [f"[{term}", f" {term}"]),
            "has_numeric_fields": any(num_term in template_content.lower() for term in ["amount", "number", "quantity", "total", "sum"] for num_term in [f"[{term}", f" {term}"])
        }

        # Identify sections in the template (all caps text followed by newline or colon)
        section_pattern = r'(?:^|\n)([A-Z][A-Z\s]+)(?:\n|:)'
        sections = re.findall(section_pattern, template_content)
        if sections:
            context["sections"] = sections

        # Identify field relationships (which fields appear near each other)
        fields = re.findall(r'\[(.*?)\]', template_content)
        for i, field in enumerate(fields):
            if i > 0:
                context["field_relationships"][field] = context["field_relationships"].get(field, []) + [fields[i-1]]
            if i < len(fields) - 1:
                context["field_relationships"][field] = context["field_relationships"].get(field, []) + [fields[i+1]]

        return context

    def _determine_field_type(self, field: str, context: Dict[str, Any] = None) -> Tuple[str, Dict[str, Any]]:
        """
        Determine the most appropriate field type based on field name

        This method is generic and works with any type of field,
        using common patterns in field names to determine the appropriate type.

        Args:
            field: The field name
            context: Template context information (optional, not used in generic implementation)

        Returns:
            Tuple of (field_type, validation_dict)
        """
        field_lower = field.lower()
        validation = {}

        # Check for date fields
        if any(date_term in field_lower for date_term in ["date", "day", "month", "year", "dob", "birth"]):
            return "date", {}

        # Check for multi-line text fields
        if any(addr_term in field_lower for addr_term in ["address", "description", "details", "comments", "notes", "paragraph"]):
            return "textarea", {}

        # Check for numeric fields
        if any(num_term in field_lower for num_term in ["amount", "number", "quantity", "count", "total", "sum", "price", "cost"]):
            # Add validation for numeric fields
            validation = {
                "min": 0,
                "step": 0.01 if "rate" in field_lower or "percentage" in field_lower else 1
            }
            return "number", validation

        # Special handling for age field
        if "age" in field_lower:
            validation = {
                "min": 18,  # Legal age minimum
                "max": 100,
                "step": 1
            }
            return "number", validation

        # Check for email fields
        if "email" in field_lower or "e-mail" in field_lower:
            validation = {"pattern": r"[^@]+@[^@]+\.[^@]+"}
            return "text", validation

        # Check for phone fields
        if any(phone_term in field_lower for phone_term in ["phone", "telephone", "mobile", "cell"]):
            validation = {"pattern": r"\d{3}[-.\s]?\d{3}[-.\s]?\d{4}"}
            return "text", validation

        # Default to text field
        return "text", {}

    def _get_field_category(self, field: str) -> str:
        """
        Determine the category of a field for grouping

        This is a generic implementation that categorizes fields based on common patterns
        in field names, without being specific to any particular document type.

        Args:
            field: The field name

        Returns:
            Category name
        """
        field_lower = field.lower()

        # Basic categories that apply to any type of form
        if any(term in field_lower for term in ["name", "email", "phone", "address", "contact"]):
            return "contact_info"
        elif any(term in field_lower for term in ["date", "time", "day", "month", "year"]):
            return "dates"
        elif any(term in field_lower for term in ["amount", "number", "quantity", "price", "cost", "payment"]):
            return "numbers"
        elif any(term in field_lower for term in ["description", "details", "comments", "notes"]):
            return "text_content"
        else:
            # Default category
            return "other"

    def _match_with_user_info(self, field: str, user_info: Dict[str, str]) -> str:
        """
        Match a field with user information using multiple strategies

        Args:
            field: The field name
            user_info: Dictionary of user information

        Returns:
            Default value for the field
        """
        if not user_info:
            return ""

        field_lower = field.lower()

        # Print debug information
        print(f"Matching field: {field} with user_info: {user_info}")

        # Define comprehensive field mappings for better matching
        field_mappings = {
            "name": ["name", "full name", "legal name", "employee name", "client name", "customer name", "person name", "party name", "individual name", "first name", "last name", "full_name", "legal_name"],
            "first_name": ["first name", "first", "given name", "forename"],
            "last_name": ["last name", "last", "surname", "family name"],
            "address": ["address", "residence", "location", "place", "home address", "office address", "mailing address", "street address", "postal address", "street", "city", "state", "zip", "postal code"],
            "email": ["email", "e-mail", "mail", "electronic mail", "email address", "e-mail address", "e mail"],
            "phone": ["phone", "telephone", "mobile", "contact number", "cell", "phone number", "mobile number", "contact", "tel", "phone no", "telephone no"],
            "company": ["company", "employer", "organization", "firm", "business", "company name", "corporation", "entity", "enterprise", "employer name", "business name"],
            "date": ["date", "agreement date", "contract date", "start date", "effective date", "execution date", "signing date", "commencement date", "termination date", "end date"],
            "position": ["position", "job title", "designation", "role", "job position", "title", "occupation", "function", "job", "employment position"],
            "salary": ["salary", "compensation", "pay", "remuneration", "wage", "income", "earnings", "payment", "annual salary", "monthly salary", "salary amount"],
            "term": ["term", "duration", "period", "length", "timeframe", "time period", "contract term", "agreement term"],
            "amount": ["amount", "sum", "fee", "payment amount", "total", "cost", "price", "value", "total amount", "payment"],
            "rate": ["rate", "interest rate", "percentage", "fee rate", "hourly rate", "rate of interest", "interest"],
            "description": ["description", "details", "specification", "information", "particulars", "job description", "service description"]
        }

        # Add more specific field mappings for common document fields
        specific_mappings = {
            "employee_name": ["name", "employee", "full name"],
            "employer_name": ["company", "employer", "organization", "business"],
            "client_name": ["client", "customer", "name", "full name"],
            "vendor_name": ["vendor", "supplier", "provider", "seller"],
            "customer_name": ["customer", "client", "buyer", "purchaser"],
            "party_name": ["party", "participant", "name"],
            "effective_date": ["date", "effective date", "start date", "commencement date"],
            "termination_date": ["end date", "termination date", "expiry date", "expiration date"],
            "payment_amount": ["amount", "payment", "sum", "total", "fee"],
            "job_title": ["position", "title", "job", "role", "designation"],
            "work_location": ["location", "workplace", "office", "place of work", "work address"],
            "contract_term": ["term", "duration", "period", "length", "timeframe"]
        }

        # Combine all mappings
        all_mappings = {**field_mappings, **specific_mappings}

        # Strategy 1: Direct exact match
        if field_lower in user_info:
            return user_info[field_lower]

        # Strategy 2: Case-insensitive partial match with improved logic
        for key, value in user_info.items():
            # Check if the field name contains the user info key or vice versa
            if key.lower() in field_lower or field_lower in key.lower():
                return value

        # Strategy 3: Using field mappings with improved matching
        for info_key, mapped_terms in all_mappings.items():
            # Check if the field matches any of the mapped terms
            if any(term.lower() in field_lower for term in mapped_terms) or any(field_lower in term.lower() for term in mapped_terms):
                # Check if any of the mapped keys exist in user_info
                for key in user_info:
                    if (key.lower() == info_key.lower() or
                        any(term.lower() in key.lower() for term in mapped_terms) or
                        any(key.lower() in term.lower() for term in mapped_terms)):
                        return user_info[key]

        # Strategy 4: Smart matching for composite fields
        # For full name when we have first and last name
        if "name" in field_lower and "first_name" in user_info and "last_name" in user_info:
            return f"{user_info['first_name']} {user_info['last_name']}"

        # For address when we have address components
        if "address" in field_lower:
            address_components = []
            for component in ["street", "city", "state", "zip", "postal_code"]:
                if component in user_info:
                    address_components.append(user_info[component])
            if address_components:
                return ", ".join(address_components)

        # Strategy 5: Fallback for common fields
        # For name fields
        if "name" in field_lower and any(name_key in user_info for name_key in ["name", "full_name", "employee_name", "client_name"]):
            for name_key in ["name", "full_name", "employee_name", "client_name"]:
                if name_key in user_info:
                    return user_info[name_key]

        # For address fields
        if "address" in field_lower and "address" in user_info:
            return user_info["address"]

        # For date fields
        if "date" in field_lower and any(date_key in user_info for date_key in ["date", "start_date", "agreement_date", "effective_date"]):
            for date_key in ["date", "start_date", "agreement_date", "effective_date"]:
                if date_key in user_info:
                    return user_info[date_key]

        # For company/employer fields
        if any(term in field_lower for term in ["company", "employer", "organization", "business"]):
            for company_key in ["company", "employer", "organization", "business", "company_name", "employer_name"]:
                if company_key in user_info:
                    return user_info[company_key]

        # No match found
        return ""

    def _create_field_label(self, field: str) -> str:
        """
        Create a user-friendly label for a field

        Args:
            field: The field name

        Returns:
            User-friendly label
        """
        # Special mappings for legal document contexts
        legal_label_mappings = {
            'court_name': 'Court Name',
            'court_location': 'Court Location',
            'case_subject': 'Case Subject/Matter',
            'petitioner_name': 'Petitioner Name',
            'petitioner_address': 'Petitioner Address',
            'respondent_name': 'Respondent Name',
            'respondent_address': 'Respondent Address',
            'case_number': 'Case Number/Details',
            'deponent_name': 'Deponent Name',
            'father_name': 'Father\'s Name',
            'age': 'Age',
            'workplace': 'Workplace/Occupation',
            'verification_location': 'Verification Location',
            'verification_date': 'Verification Date',
        }
        
        # Check if we have a specific mapping for this field
        if field.lower() in legal_label_mappings:
            return legal_label_mappings[field.lower()]
        
        # Replace underscores with spaces
        label = field.replace("_", " ")

        # Capitalize first letter of each word
        label = label.title()

        # Improve readability for common abbreviations and terms
        label = label.replace("Id", "ID")
        label = label.replace("Ssn", "SSN")
        label = label.replace("Dob", "Date of Birth")
        label = label.replace("Field ", "")  # Remove generic "Field" prefix

        return label

    def _create_placeholder(self, field: str, field_type: str) -> str:
        """
        Create a helpful placeholder text for a field

        Args:
            field: The field name
            field_type: The field type

        Returns:
            Placeholder text
        """
        field_lower = field.lower()

        # Legal document specific placeholders
        legal_placeholders = {
            'court_name': 'e.g., District Court',
            'court_location': 'e.g., New Delhi',
            'case_subject': 'Brief description of the case matter',
            'petitioner_name': 'Full name of the petitioner',
            'petitioner_address': 'Complete address of petitioner',
            'respondent_name': 'Full name of the respondent',
            'respondent_address': 'Complete address of respondent',
            'case_number': 'Case reference number if available',
            'deponent_name': 'Full name of the person making the affidavit',
            'father_name': 'Father\'s full name',
            'age': 'Age in years',
            'workplace': 'Name of employer/workplace',
            'verification_location': 'e.g., New Delhi',
            'verification_date': 'Date of verification',
        }
        
        # Check for specific legal field placeholders
        if field_lower in legal_placeholders:
            return legal_placeholders[field_lower]

        if field_type == "date":
            return "Select a date"

        if "name" in field_lower:
            return "Enter full name"

        if "email" in field_lower:
            return "<EMAIL>"

        if "phone" in field_lower:
            return "************"

        if "address" in field_lower:
            return "Enter complete address"

        if field_type == "number":
            if "salary" in field_lower:
                return "Enter annual salary"
            if "amount" in field_lower or "payment" in field_lower:
                return "Enter amount"
            if "age" in field_lower:
                return "Enter age in years"
            return "Enter number"

        # Default placeholder
        return f"Enter {field.replace('_', ' ')}"

    def _create_help_text(self, field: str, field_type: str, context: Dict[str, Any] = None) -> str:
        """
        Create helpful text for complex fields

        This method is generic and provides helpful text based on field type
        and common patterns in field names, without being specific to any document type.

        Args:
            field: The field name
            field_type: The field type
            context: Template context information (optional, not used in generic implementation)

        Returns:
            Help text or empty string
        """
        field_lower = field.lower()

        # Help text based on field type
        if field_type == "date":
            return "Select a date from the calendar"

        if field_type == "number":
            if "rate" in field_lower or "percentage" in field_lower:
                return "Enter the value as a decimal (e.g., 0.05 for 5%)"
            return "Enter a numeric value"

        if field_type == "textarea":
            return "Enter detailed information"

        # Help text based on field name patterns
        if "email" in field_lower:
            return "Enter a valid email address"

        if "phone" in field_lower:
            return "Enter a valid phone number (e.g., ************)"

        # No help text needed for simple fields
        return ""

    def _create_contextual_field_names(self, template_content: str, num_placeholders: int) -> List[str]:
        """Create meaningful field names based on context around dotted placeholders."""
        field_names = []
        
        # Split content into lines to analyze context
        lines = template_content.split('\n')
        placeholder_count = 0
        
        # Enhanced context mappings for legal documents
        context_mappings = [
            (r'hon.*ble.*at', ['court_name', 'court_location']),
            (r'in the matter of', ['case_subject']),
            (r'petitioner', ['petitioner_name', 'petitioner_address']),
            (r'versus', ['case_number']),
            (r'respondent', ['respondent_name', 'respondent_address']),
            (r'i,.*son of.*aged.*working', ['deponent_name', 'father_name', 'age', 'workplace']),
            (r'verified.*at.*on', ['verification_location', 'verification_date']),
        ]
        
        for line in lines:
            # Count dotted placeholders in this line
            dot_count = len(re.findall(r'…{3,}', line))
            
            if dot_count > 0:
                line_lower = line.lower().strip()
                matched = False
                
                # Try to match with context mappings
                for pattern, suggested_names in context_mappings:
                    if re.search(pattern, line_lower):
                        # Add the suggested field names for this context
                        names_to_add = min(dot_count, len(suggested_names))
                        for i in range(names_to_add):
                            if placeholder_count < num_placeholders:
                                field_names.append(suggested_names[i])
                                placeholder_count += 1
                        
                        # If more placeholders than suggested names, add numbered variants
                        for i in range(names_to_add, dot_count):
                            if placeholder_count < num_placeholders:
                                base_name = suggested_names[-1] if suggested_names else "field"
                                field_names.append(f"{base_name}_{i+1}")
                                placeholder_count += 1
                        matched = True
                        break
                
                if not matched:
                    # If no context match, create generic field names
                    for i in range(dot_count):
                        if placeholder_count < num_placeholders:
                            field_names.append(f"field_{placeholder_count + 1}")
                            placeholder_count += 1
        
        # Fill remaining placeholders if any
        while len(field_names) < num_placeholders:
            field_names.append(f"field_{len(field_names) + 1}")
            
        return field_names
