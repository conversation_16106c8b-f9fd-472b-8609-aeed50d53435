import os
from typing import Dict, List, Any, Optional, Tuple
from agents.user_data_agent import UserDataAgent
from agents.master_db_agent import MasterDBAgent
from database.master_db_manager import MasterDatabaseManager
from agents.form_creator_agent import FormCreatorAgent
from agents.document_composer_agent import DocumentComposerAgent
from agents.verification_agent import VerificationAgent
from utils.template_manager import TemplateManager

class LegalDocumentManager:
    """
    Manager for coordinating all agents in the legal document generation process.

    This manager orchestrates the workflow between different specialized agents:
    1. UserDataAgent: Extracts data from client files and creates a temporary knowledge base
    2. MasterDBAgent: Retrieves relevant clauses from the master database
    3. FormCreatorAgent: Creates dynamic forms based on templates
    4. DocumentComposerAgent: Composes the document using template, client knowledge base,
                             master DB clauses, and user instructions
    5. VerificationAgent: Verifies the document by referencing master DB clauses and client files
                         to identify red flags and inconsistencies
    """

    def __init__(self, category: str = None):
        """
        Initialize the legal document manager

        Args:
            category: Optional document category for specialized processing
        """
        self.category = category
        self.template_manager = TemplateManager()

        # Initialize agents
        self.user_data_agent = UserDataAgent()
        self.master_db_agent = MasterDBAgent(category=category)
        self.form_creator_agent = FormCreatorAgent()
        self.document_composer_agent = DocumentComposerAgent()
        self.verification_agent = VerificationAgent()

        # Initialize the hierarchical master database manager
        self.master_db_manager = MasterDatabaseManager()
        try:
            self.master_db_manager.initialize()
        except Exception as e:
            print(f"Warning: Could not initialize hierarchical master database: {e}")

    def extract_user_info(self, client_files: List[str], extraction_depth: str = "Standard") -> Dict[str, str]:
        """
        Extract user information from client files

        Note: This method is kept for compatibility but auto-fill has been disabled.
        Client files are still used for context in document generation.

        Args:
            client_files: List of client file paths
            extraction_depth: Depth of extraction ("Basic", "Standard", or "Deep")

        Returns:
            Empty dictionary (auto-fill disabled)
        """
        # Return empty dictionary instead of extracting user info (auto-fill disabled)
        return {}

    def create_form_fields(self, template_path: str, user_info: Dict[str, str]) -> List[Dict[str, Any]]:
        """
        Create form fields based on a template and user information

        Args:
            template_path: Path to the template file
            user_info: Dictionary of user information (kept for compatibility)

        Returns:
            List of form field configurations
        """
        # Get template content
        template_content = self.template_manager.get_template_content(template_path)

        # Create form fields using the enhanced form creator
        return self.form_creator_agent.create_form_fields(template_content)

    def generate_document(self,
                         template_path: str,
                         form_data: Dict[str, str],
                         user_instructions: str,
                         client_files: Optional[List[str]] = None) -> str:
        """
        Generate a legal document using the document composer agent

        Args:
            template_path: Path to the template file
            form_data: Data collected from the form
            user_instructions: Additional instructions from the user
            client_files: Optional list of client file paths

        Returns:
            Generated document
        """
        # Get template content
        template_content = self.template_manager.get_template_content(template_path)

        # Get template name for context
        template_name = os.path.basename(template_path).replace("_template.txt", "").replace("_", " ").title()

        # Only extract clauses from master DB if user specifically requests it in instructions
        # OR for verification purposes (not for automatic document generation)
        relevant_clauses = []
        
        # Check if user instructions contain requests for specific clauses from master DB
        if user_instructions and any(keyword in user_instructions.lower() for keyword in 
                                   ['clause', 'add', 'include', 'standard', 'master', 'database', 'template']):
            try:
                # Only get clauses from master DB when explicitly requested
                relevant_clauses = self.master_db_agent.get_relevant_clauses(
                    self.category or "agreement",
                    template_name,
                    user_instructions  # Use user instructions to find specific clauses
                )
                print(f"Found {len(relevant_clauses)} relevant clauses from master DB based on user instructions")
            except Exception as e:
                print(f"Error getting clauses from master DB: {e}")
                relevant_clauses = []

        # Compose document using template, form data, and only master DB clauses if requested
        document = self.document_composer_agent.compose_document(
            template_content,
            form_data,
            relevant_clauses,  # Only use master DB clauses when explicitly requested
            user_instructions,
            client_files  # Client files are used only for context, not for auto-filling
        )

        return document

    def revise_document(self,
                       document: str,
                       revision_instructions: str) -> str:
        """
        Revise a document using the document composer agent

        Args:
            document: The document to revise
            revision_instructions: Instructions for the revision

        Returns:
            Revised document
        """
        return self.document_composer_agent.revise_document(document, revision_instructions)

    def verify_document(self,
                       document: str,
                       template_path: str,
                       form_data: Dict[str, str],
                       client_files: Optional[List[str]] = None) -> Tuple[bool, List[Dict[str, Any]]]:
        """
        Verify a document using the verification agent

        Args:
            document: The document to verify
            template_path: Path to the template file
            form_data: Data collected from the form
            client_files: Optional list of client file paths

        Returns:
            Tuple containing:
            - Boolean indicating if the document has critical issues
            - List of issues found
        """
        # Get template name for context
        template_name = os.path.basename(template_path).replace("_template.txt", "").replace("_", " ").title()

        # For verification, get master DB clauses to check against standard legal practices
        master_db_clauses = []
        try:
            master_db_clauses = self.master_db_agent.get_relevant_clauses(
                self.category or "agreement",
                template_name,
                f"standard clauses for {template_name} verification"
            )
            print(f"Found {len(master_db_clauses)} master DB clauses for verification")
        except Exception as e:
            print(f"Error getting master DB clauses for verification: {e}")

        # Verify document using master DB clauses for legal standard verification
        # and client files for fact verification
        has_critical_issues, issues = self.verification_agent.verify_document(
            document,
            self.category or "agreement",
            template_name,
            client_files=client_files,
            master_db_clauses=master_db_clauses  # Use master DB clauses for legal verification
        )

        return has_critical_issues, issues
