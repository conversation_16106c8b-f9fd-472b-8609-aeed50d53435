import os
from crewai import Agent, Task
from langchain_openai import AzureChatOpenAI
from utils.api_key import get_azure_openai_config
from typing import Dict, List, Any, Tuple

class VerificationAgent:
    def __init__(self):
        """
        Initialize the verification agent

        This agent verifies the generated document draft by:
        1. Referencing the master database extracted clauses
        2. Checking against the client files that were selected
        3. Identifying any red flags, inconsistencies, or errors
        4. Providing detailed feedback on issues found
        """
        azure_config = get_azure_openai_config()
        self.llm = AzureChatOpenAI(
            deployment_name=azure_config['deployment_name'],
            api_key=azure_config['api_key'],
            azure_endpoint=azure_config['endpoint'],
            api_version=azure_config['api_version']
        )

    def create_agent(self) -> Agent:
        """
        Create the CrewAI agent for document verification

        Returns:
            CrewAI Agent
        """
        return Agent(
            role="Legal Document Verification Specialist",
            goal="Verify legal documents for correctness, consistency, and potential issues by cross-referencing multiple sources",
            backstory=(
                "You are a highly experienced legal document reviewer with years of experience "
                "in identifying inconsistencies, errors, and potential legal issues in documents. "
                "You have a keen eye for detail and can spot problems that others might miss. "
                "You excel at cross-referencing documents against multiple sources, including client files "
                "and standard legal clauses from master databases. You can identify when a document "
                "deviates from standard practices or contains inconsistencies with client information. "
                "Your expertise helps ensure that legal documents are accurate, consistent, and "
                "legally sound before they are finalized."
            ),
            verbose=True,
            llm=self.llm,
            allow_delegation=False
        )

    def create_task(self, document_type: str, template_name: str) -> Task:
        """
        Create a task for the verification agent

        Args:
            document_type: Type of document (e.g., "agreement", "contract")
            template_name: Name of the template used

        Returns:
            CrewAI Task
        """
        return Task(
            description=(
                f"Verify the {document_type} document based on the template '{template_name}'. "
                f"Cross-reference the document against the master database extracted clauses and client files. "
                f"Check for inconsistencies, errors, missing information, and potential legal issues. "
                f"Identify any red flags that might need attention before finalizing the document. "
                f"Pay special attention to areas where the document deviates from standard practices "
                f"or contains information inconsistent with client files."
            ),
            expected_output=(
                "A list of issues found in the document, categorized by severity (Critical, Important, Minor), "
                "with specific references to the problematic sections and suggestions for improvement. "
                "Each issue should include a clear explanation of why it's a problem and how it relates to "
                "the master database clauses or client files."
            ),
            agent=self.create_agent()
        )

    def verify_document(self, document: str, document_type: str, template_name: str,
                     client_files: List[str] = None, master_db_clauses: List[Dict[str, Any]] = None) -> Tuple[bool, List[Dict[str, Any]]]:
        """
        Verify a document for correctness, consistency, and potential issues

        This method verifies the generated document draft by referencing the master database
        extracted clauses and client files to identify any red flags, inconsistencies, or errors.

        Args:
            document: The document to verify
            document_type: Type of document (e.g., "agreement", "contract")
            template_name: Name of the template used
            client_files: Optional list of client file paths to use as reference
            master_db_clauses: Optional list of relevant clauses from master database

        Returns:
            Tuple containing:
            - Boolean indicating if the document has critical issues
            - List of issues found, each with severity, description, location, suggestion, and reference
        """
        # Extract context from client files if provided
        client_context = ""
        if client_files and len(client_files) > 0:
            try:
                from database.vector_db import VectorDatabase

                # Create a temporary vector database for verification
                temp_db = VectorDatabase("temp_verification_db")

                # The files should already be vectorized in the app.py workflow,
                # but we'll check and index them if needed
                if not temp_db.load_index():
                    temp_db.index_files(client_files)

                # Extract key sections from the document for targeted verification
                doc_sections = document.split("\n\n")

                # Create more comprehensive verification queries
                verification_queries = [
                    "Extract key facts, dates, names, and amounts for verification",
                    "Find information about parties involved in the agreement",
                    "Extract information about terms, conditions, and requirements",
                    "Find information about payment terms or financial details",
                    "Find information about deadlines, timeframes, or schedules",
                    "Find information about legal obligations or responsibilities",
                    "Find information about dispute resolution or termination clauses",
                    "Find information about confidentiality or non-disclosure provisions"
                ]

                # Add document-specific queries based on document content
                # Use more sections for more comprehensive verification
                for section in doc_sections[:5]:  # Use first few sections to create targeted queries
                    if len(section.strip()) > 50:
                        verification_queries.append(f"Find information related to: {section[:100]}")

                # Extract key terms from the document for verification
                import re
                key_terms = re.findall(r'\b[A-Z][a-z]{2,}\b', document[:1000])  # Look at first 1000 chars for key terms
                for term in key_terms[:5]:  # Limit to first 5 terms to avoid too many queries
                    verification_queries.append(f"Find information related to {term}")

                # Search for relevant information with higher k value for more comprehensive verification
                client_knowledge = []
                for query in verification_queries:
                    results = temp_db.search(query, k=3)  # Increased from k=2 to k=3
                    for result in results:
                        if result not in client_knowledge:
                            client_knowledge.append(result)

                # Format client knowledge for the prompt
                if client_knowledge:
                    client_context = "\n\nCLIENT KNOWLEDGE BASE (for cross-verification):\n"
                    for i, knowledge in enumerate(client_knowledge):
                        source = knowledge['metadata'].get('source', 'Unknown document')
                        content = knowledge['content']
                        client_context += f"Reference {i+1} (from {os.path.basename(source)}):\n{content}\n\n"

            except Exception as e:
                print(f"Error accessing client files for verification: {e}")
                client_context = "\n\nNote: Client files were provided but could not be accessed for verification."

        # Format master database clauses if provided
        master_db_context = ""
        if master_db_clauses and len(master_db_clauses) > 0:
            master_db_context = "\n\nMASTER DATABASE REFERENCES (for legal standard verification):\n"
            for i, clause in enumerate(master_db_clauses):
                source = clause.get('metadata', {}).get('source', 'Unknown source')
                content = clause.get('content', '')
                if content:
                    master_db_context += f"Standard Clause {i+1} (from {source}):\n{content}\n\n"

        prompt = f"""
        You are a legal document verification specialist tasked with reviewing a {document_type} document draft
        based on the '{template_name}' template. Your job is to cross-reference the document against the master
        database extracted clauses and client files to identify any red flags, inconsistencies, or errors.

        DOCUMENT TO VERIFY ({document_type.upper()} based on {template_name} template):
        {document}

        REFERENCE SOURCES:
        {client_context}
        {master_db_context}

        Perform a comprehensive verification by analyzing the document for the following issues:

        INCONSISTENCIES AND FACTUAL ERRORS:
        1. Inconsistencies in terms, names, dates, or amounts between the document and client knowledge base
        2. Factual errors or inaccuracies when cross-referenced with client information
        3. Contradictions within the document itself

        MISSING OR INCOMPLETE INFORMATION:
        4. Missing or incomplete information that should be included based on client files
        5. Required clauses or sections that are absent but present in the master database references
        6. Incomplete legal protections compared to master database references

        LEGAL AND COMPLIANCE ISSUES:
        7. Potential legal issues or vulnerabilities when compared to standard clauses
        8. Compliance issues with standard legal practices
        9. Deviations from standard practices that could create legal risks

        CLARITY AND STRUCTURE ISSUES:
        10. Ambiguous language or clauses that could be clarified
        11. Formatting or structural problems
        12. Vague terms that could lead to misinterpretation

        For each issue found, provide:
        - Severity (Critical, Important, Minor)
        - Description of the issue
        - Location in the document (section or clause number)
        - Suggestion for improvement
        - Reference to the source (client file or master database clause) that contradicts or provides the correct information

        SEVERITY GUIDELINES:
        - Critical: Issues that could invalidate the document, create significant legal risks, or fundamentally misrepresent facts
        - Important: Issues that should be addressed but don't invalidate the document
        - Minor: Stylistic issues, minor inconsistencies, or suggestions for improvement

        Format your response as a JSON array of issues, with each issue having the following structure:
        {{
            "severity": "Critical|Important|Minor",
            "description": "Description of the issue",
            "location": "Section or clause reference",
            "suggestion": "Suggestion for improvement",
            "reference": "Reference to the source that contradicts or provides the correct information"
        }}

        If no issues are found, return an empty array.

        IMPORTANT: Return ONLY the JSON array without any additional text, explanation, or formatting.
        """

        # Generate verification results using the LLM
        response = self.llm.invoke(prompt)

        try:
            # Try to parse the response as JSON
            import json
            import re

            # Extract JSON array from response if it's not already in JSON format
            content = response.content
            json_match = re.search(r'\[\s*{.*}\s*\]', content, re.DOTALL)
            if json_match:
                content = json_match.group(0)

            issues = json.loads(content)

            # Ensure all issues have the required fields
            for issue in issues:
                # Ensure required fields exist
                for field in ["severity", "description", "location", "suggestion"]:
                    if field not in issue:
                        issue[field] = "Not specified"

            # Check if there are any critical issues
            has_critical_issues = any(issue.get("severity") == "Critical" for issue in issues)

            return has_critical_issues, issues
        except Exception as e:
            # If parsing fails, return a generic error
            return True, [{
                "severity": "Critical",
                "description": f"Error parsing verification results: {str(e)}",
                "location": "General",
                "suggestion": "Please review the document manually or try verification again."
            }]

    def format_issues_for_display(self, issues: List[Dict[str, Any]]) -> str:
        """
        Format issues for display in the UI

        Args:
            issues: List of issues found

        Returns:
            Formatted string for display
        """
        if not issues:
            return "✅ No issues found. The document appears to be correct and consistent."

        result = "# Document Verification Results\n\n"

        # Group issues by severity
        critical_issues = [i for i in issues if i.get("severity") == "Critical"]
        important_issues = [i for i in issues if i.get("severity") == "Important"]
        minor_issues = [i for i in issues if i.get("severity") == "Minor"]

        # Add critical issues
        if critical_issues:
            result += "## ❌ Critical Issues\n\n"
            for i, issue in enumerate(critical_issues, 1):
                result += f"### Issue {i}: {issue.get('description')}\n"
                result += f"**Location**: {issue.get('location')}\n"
                result += f"**Suggestion**: {issue.get('suggestion')}\n"
                result += "\n"

        # Add important issues
        if important_issues:
            result += "##  Important Issues\n\n"
            for i, issue in enumerate(important_issues, 1):
                result += f"### Issue {i}: {issue.get('description')}\n"
                result += f"**Location**: {issue.get('location')}\n"
                result += f"**Suggestion**: {issue.get('suggestion')}\n"
                result += "\n"

        # Add minor issues
        if minor_issues:
            result += "## ℹ️ Minor Issues\n\n"
            for i, issue in enumerate(minor_issues, 1):
                result += f"### Issue {i}: {issue.get('description')}\n"
                result += f"**Location**: {issue.get('location')}\n"
                result += f"**Suggestion**: {issue.get('suggestion')}\n"
                result += "\n"

        return result
