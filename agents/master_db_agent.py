import os
from crewai import Agent, Task
from langchain_openai import AzureChatOpenAI
from database.vector_db import VectorDatabase
from database.master_db_manager import MasterDatabaseManager
from utils.api_key import get_azure_openai_config
from typing import Dict, List, Any, Optional
import logging

logger = logging.getLogger(__name__)

class MasterDBAgent:
    def __init__(self, category: str = None, subcategory: str = None):
        """
        Initialize the master database agent

        The master database has a hierarchical structure:
        - Top level: Categories (agreements, contracts, notices, etc.)
        - Second level: Subcategories (employment agreement, etc.)
        - Third level: Document samples

        Args:
            category: Main category for master_db (e.g., "agreements", "contracts")
            subcategory: Subcategory within the main category (e.g., "employment" for employment agreements)
        """
        azure_config = get_azure_openai_config()
        self.llm = AzureChatOpenAI(
            deployment_name=azure_config['deployment_name'],
            api_key=azure_config['api_key'],
            azure_endpoint=azure_config['endpoint'],
            api_version=azure_config['api_version']
        )
        self.category = category
        self.subcategory = subcategory
        self.master_db_path = "master_db"

        # Initialize the hierarchical master database manager
        self.master_db_manager = MasterDatabaseManager(self.master_db_path)

        # Ensure the hierarchical vector database is initialized
        self._ensure_vectorized()

    def _ensure_vectorized(self) -> None:
        """
        Check if the hierarchical master database is vectorized, and vectorize it if not
        """
        try:
            logger.info("Checking if master database is vectorized...")
            self.master_db_manager.initialize()
            logger.info("Master database vectorization verified")
        except Exception as e:
            logger.error(f"Error initializing master database: {e}")
            raise

    def vectorize_master_db(self, force_rebuild: bool = False) -> None:
        """
        Vectorize the entire master database with hierarchical structure

        Args:
            force_rebuild: Whether to force rebuild all vector stores
        """
        logger.info("Starting hierarchical vectorization of master database...")
        try:
            self.master_db_manager.initialize(force_rebuild=force_rebuild)
            logger.info("Master database vectorized successfully")

            # Print statistics
            stats = self.master_db_manager.get_statistics()
            logger.info(f"Vectorization complete: {stats['total_categories']} categories, "
                       f"{stats['total_files']} files processed")

        except Exception as e:
            logger.error(f"Error vectorizing master database: {e}")
            raise

    def get_relevant_context(self, template_path: str, query: str, k: int = 5) -> List[Dict[str, Any]]:
        """
        Get relevant context from the master database for a specific template and query
        using the hierarchical vectorization system

        Args:
            template_path: Path to the template file
            query: Query string for context retrieval
            k: Number of documents to retrieve

        Returns:
            List of relevant documents with metadata
        """
        try:
            # Use the hierarchical system to get targeted context
            documents = self.master_db_manager.get_relevant_context(template_path, query, k)

            # Convert to the expected format
            results = []
            for doc in documents:
                results.append({
                    "content": doc.page_content,
                    "metadata": doc.metadata,
                    "source": doc.metadata.get("source", ""),
                    "category": doc.metadata.get("category", ""),
                    "filename": doc.metadata.get("filename", "")
                })

            logger.info(f"Retrieved {len(results)} relevant documents for template: {template_path}")
            return results

        except Exception as e:
            logger.error(f"Error getting relevant context: {e}")
            return []

    def create_enhanced_task(self, template_path: str, query: str, context: Dict[str, Any]) -> Task:
        """
        Create an enhanced task that uses hierarchical context retrieval

        Args:
            template_path: Path to the template file
            query: Query for context retrieval
            context: Additional context information

        Returns:
            CrewAI Task with relevant context
        """
        # Get relevant context from the hierarchical database
        relevant_docs = self.get_relevant_context(template_path, query, k=5)

        # Format the context for the task
        context_text = ""
        if relevant_docs:
            context_text = "\n\nRelevant legal precedents and clauses from master database:\n"
            for i, doc in enumerate(relevant_docs, 1):
                context_text += f"\n{i}. From {doc['filename']} (Category: {doc['category']}):\n"
                context_text += f"{doc['content'][:500]}...\n"

        return Task(
            description=(
                f"Analyze the template and provide relevant legal clauses and information. "
                f"Template path: {template_path}\n"
                f"Query context: {query}\n"
                f"Additional context: {context}\n"
                f"{context_text}\n\n"
                f"Based on this information, provide relevant legal clauses, standard terms, "
                f"and specific requirements that should be considered for this document type."
            ),
            expected_output=(
                "A comprehensive analysis including: 1) Relevant legal clauses from the master database, "
                "2) Standard terms and conditions, 3) Specific legal requirements, "
                "4) Recommendations for document structure and content."
            ),
            agent=self.create_agent()
        )

    def create_agent(self) -> Agent:
        """
        Create the CrewAI agent

        Returns:
            CrewAI Agent
        """
        return Agent(
            role="Legal Knowledge Specialist",
            goal="Find relevant legal clauses and information from the master database",
            backstory=(
                "You are a legal expert with extensive knowledge of contract law and legal documentation. "
                "You can quickly identify relevant legal clauses and information that should be included "
                "in various types of legal documents based on the specific context and requirements."
            ),
            verbose=True,
            llm=self.llm,
            allow_delegation=False
        )

    def create_task(self, document_type: str, template_name: str, context: Dict[str, Any]) -> Task:
        """
        Create a task for the agent

        Args:
            document_type: Type of document (e.g., "agreement", "contract")
            template_name: Name of the template (e.g., "employment")
            context: Additional context information

        Returns:
            CrewAI Task
        """
        return Task(
            description=(
                f"Find relevant legal clauses and information from the master database for a {document_type} "
                f"based on the template '{template_name}'. Consider the following context: {context}. "
                f"Extract key clauses, standard terms, and any specific legal requirements that should be "
                f"included in this document."
            ),
            expected_output=(
                "A list of relevant legal clauses and information with their sources, "
                "organized by section or topic."
            ),
            agent=self.create_agent()
        )

    def get_relevant_clauses(self, document_type: str, template_name: str, context: Dict[str, Any]) -> List[Dict[str, Any]]:
        """
        Get relevant clauses from the master database

        This method will search the vectorized master database but filter results to only include
        documents from the relevant category and subcategory.

        For example, if the user is drafting an employment agreement, this will only search
        within the "agreements" category and specifically for "employment" related documents.

        Args:
            document_type: Type of document (e.g., "agreement", "contract")
            template_name: Name of the template (e.g., "employment")
            context: Additional context information

        Returns:
            List of relevant clauses with their sources
        """
        # Determine category and subcategory from inputs
        category = self.category or document_type.lower()
        subcategory = self.subcategory or template_name.lower()

        # Create a vector database instance for the master database
        # This uses the pre-vectorized database that was created during initialization
        vector_db = VectorDatabase("master_db")

        # Create a search query based on the document type and template
        query = f"{document_type} {template_name} clauses"

        # Add context to the query to make it more specific
        for key, value in context.items():
            if isinstance(value, str):
                query += f" {key}: {value}"

        # Search the master database - get more results initially for filtering
        all_results = vector_db.search(query, k=30)

        # Filter results to only include documents from the relevant category and subcategory
        # This ensures we only get results from the appropriate section of the master database
        filtered_results = self._filter_results_by_category(all_results, category, subcategory)

        # Return top 10 filtered results or all if less than 10
        return filtered_results[:10] if len(filtered_results) > 10 else filtered_results

    def _filter_results_by_category(self, results: List[Dict[str, Any]],
                                   category: str, subcategory: Optional[str] = None) -> List[Dict[str, Any]]:
        """
        Filter search results to only include documents from the specified category and subcategory

        The master database has a hierarchical structure:
        - Top level: Categories (agreements, contracts, notices, etc.)
        - Second level: Subcategories (employment agreement, etc.)
        - Third level: Document samples

        This method filters results to match the requested category and subcategory.

        Args:
            results: Search results from the vector database
            category: Category to filter by (e.g., "agreements")
            subcategory: Optional subcategory to filter by (e.g., "employment")

        Returns:
            Filtered list of results
        """
        filtered_results = []

        for result in results:
            # Get the source file path from metadata
            file_path = result.get("metadata", {}).get("source", "")

            if not file_path:
                continue

            # Convert to lowercase for case-insensitive comparison
            file_path_lower = file_path.lower()
            category_lower = category.lower()

            # Check if the file path contains the category
            # For example, if category is "agreements", check if "agreements" is in the path
            if category_lower in file_path_lower:
                # If subcategory is specified, check if the file path contains it
                if subcategory:
                    subcategory_lower = subcategory.lower()
                    # For employment agreements, check if filename contains "employment"
                    filename = os.path.basename(file_path).lower()
                    if subcategory_lower in filename:
                        filtered_results.append(result)
                else:
                    # If no subcategory specified, include all results from the category
                    filtered_results.append(result)

        # Sort results by relevance score (lower score is better in FAISS)
        filtered_results.sort(key=lambda x: x.get("score", float("inf")))

        return filtered_results
