import os
from crewai import Agent, Task
from langchain_openai import AzureChatOpenAI
from utils.document_processor import DocumentProcessor
from utils.api_key import get_azure_openai_config
from typing import Dict, List, Any, Optional

class DocumentComposerAgent:
    def __init__(self):
        """
        Initialize the document composer agent

        This agent takes the template selected by user, refers to knowledge base from the client
        database that has been vectorized for the selected files only for this session only.
        It also refers to the master db agent that has extracted relevant clauses from the database
        and it takes user prompt for particular clauses etc. and the filled form.
        It composes the draft document by integrating all these sources of information.
        """
        azure_config = get_azure_openai_config()
        self.llm = AzureChatOpenAI(
            deployment_name=azure_config['deployment_name'],
            api_key=azure_config['api_key'],
            azure_endpoint=azure_config['endpoint'],
            api_version=azure_config['api_version']
        )

    def create_agent(self) -> Agent:
        """
        Create the CrewAI agent for document composition

        Returns:
            CrewAI Agent
        """
        return Agent(
            role="Legal Document Composer",
            goal="Create professional, accurate, and legally sound documents by integrating multiple sources of information",
            backstory=(
                "You are a highly skilled legal document specialist with years of experience drafting "
                "various types of legal documents. You have an eye for detail and ensure that all "
                "documents are clear, concise, and legally sound. You understand the importance of "
                "precision in legal language and can adapt to different document types and requirements. "
                "Your expertise includes integrating information from multiple sources, including client "
                "files, master databases of legal clauses, and user instructions to create comprehensive "
                "and tailored legal documents that meet specific needs."
            ),
            verbose=True,
            llm=self.llm,
            allow_delegation=False
        )

    def create_task(
        self,
        user_instructions: str
    ) -> Task:
        """
        Create a task for the document composer agent

        Args:
            user_instructions: Additional instructions from the user for particular clauses

        Returns:
            CrewAI Task
        """
        return Task(
            description=(
                f"Compose a legal document based on the provided template and filled form data. "
                f"Reference the knowledge base created from the client files that have been vectorized for this session only. "
                f"Incorporate relevant clauses from the master database where appropriate. "
                f"Process the user's specific instructions for particular clauses: {user_instructions}. "
                f"Ensure the document is professional, accurate, and legally sound."
            ),
            expected_output=(
                "A complete legal document in text format, ready for review and download. "
                "The document should incorporate information from the client knowledge base, "
                "relevant clauses from the master database, and follow the user's specific instructions."
            ),
            agent=self.create_agent()
        )

    def compose_document(
        self,
        template_content: str,
        form_data: Dict[str, str],
        relevant_clauses: List[Dict[str, Any]],
        user_instructions: str,
        client_files: Optional[List[str]] = None
    ) -> str:
        """
        Compose a legal document

        This method takes the user-selected template, references the knowledge base from client files
        that have been vectorized for the current session only, consults the master_db for relevant
        clauses, processes user prompts for specific clauses, and uses the filled form to compose
        the document draft.

        Args:
            template_content: Content of the template selected by the user
            form_data: Data collected from the form that was filled out
            relevant_clauses: Relevant clauses from the master database extracted by the master_db agent
            user_instructions: Additional instructions from the user for particular clauses
            client_files: Optional list of client file paths that have been vectorized for this session

        Returns:
            Composed document draft
        """
        # Fill template with form data
        document = DocumentProcessor.fill_template(template_content, form_data)

        # Extract content from client files ONLY for context when needed
        client_context = ""
        client_knowledge = []

        # Only extract client file information if user explicitly requests it in instructions
        if (client_files and len(client_files) > 0 and user_instructions and 
            any(keyword in user_instructions.lower() for keyword in 
                ['reference', 'use', 'from', 'client', 'file', 'document', 'extract', 'based on'])):
            
            # Create a vector database from the client files for the current session only
            try:
                from database.vector_db import VectorDatabase
                temp_db = VectorDatabase("temp_composer_db")

                # The files should already be vectorized in the app.py workflow,
                # but we'll check and index them if needed
                if not temp_db.load_index():
                    temp_db.index_files(client_files)

                # Create targeted queries based on user instructions ONLY
                queries = []
                
                # Extract key terms from user instructions to find relevant information
                if user_instructions:
                    import re
                    # Look for specific requests in user instructions
                    instruction_terms = re.findall(r'\b[A-Z][a-z]{2,}\b', user_instructions)
                    for term in instruction_terms[:3]:  # Limit to avoid too many queries
                        queries.append(f"Find information related to {term}")
                    
                    # Add a general query based on the user's request
                    queries.append(f"Find information relevant to: {user_instructions[:200]}")

                # Search for relevant information using each query
                for query in queries:
                    results = temp_db.search(query, k=2)
                    for result in results:
                        # Avoid duplicates
                        if result not in client_knowledge:
                            client_knowledge.append(result)

                # Format the client knowledge for the prompt
                if client_knowledge:
                    client_context = "CLIENT KNOWLEDGE BASE (used only per user instructions):\n\n"
                    for i, knowledge in enumerate(client_knowledge):
                        source = knowledge['metadata'].get('source', 'Unknown document')
                        content = knowledge['content']
                        client_context += f"Reference {i+1} (from {os.path.basename(source)}):\n{content}\n\n"

            except Exception as e:
                print(f"Error using vector search for client files: {e}")
                client_context = ""

        # Extract context fields from form_data (only explicit form data)
        context_fields = {k: v for k, v in form_data.items() if k.startswith("context_")}
        context_info = ""
        if context_fields:
            context_info = "ADDITIONAL CONTEXT (from form data only):\n\n"
            for key, value in context_fields.items():
                context_info += f"{key.replace('context_', '')}: {value}\n"
            context_info += "\n"

        # Format the relevant clauses from the master database
        master_db_context = ""
        if relevant_clauses and len(relevant_clauses) > 0:
            master_db_context = "RELEVANT CLAUSES (extracted from master database by the master_db agent):\n\n"
            master_db_context += self._format_clauses(relevant_clauses)

        # Create a prompt for the LLM to compose the document
        prompt = f"""
        You are a legal document composer tasked with creating a professional, accurate, and legally sound document.

        TEMPLATE AND FORM DATA:
        I have a legal document template that has been filled with form data. Here is the current document:

        {document}

        {master_db_context}

        {client_context}

        {context_info}

        USER INSTRUCTIONS FOR SPECIFIC CLAUSES:
        {user_instructions}

        Please compose a final document by:
        1. Using the template and filled form data as the PRIMARY and MAIN source
        2. ONLY use information from client files if the user has explicitly requested it in their instructions
        3. Incorporate clauses from the master database ONLY when provided (when user requests specific legal clauses)
        4. Follow the user's specific instructions for particular clauses
        5. Fill ONLY the placeholders that have corresponding values in the form data
        6. Do NOT add any personal information, names, addresses, or details that are not in the form data
        7. Leave placeholders unfilled if no corresponding form data exists
        8. Make sure the language is clear, professional, and legally sound
        9. Maintain the document's structure and formatting

        CRITICAL: Do NOT extract or infer personal information from any source other than the form data that was explicitly provided by the user. Client files should only be referenced if the user specifically requested information from them in their instructions.

        IMPORTANT: Return ONLY the complete, composed document without any additional commentary, explanations, or notes. Do not include any text outside the document itself. Do not include any disclaimers, recommendations, or suggestions. Just provide the clean document text that follows the template format.
        """

        # Generate composed document using the LLM
        response = self.llm.invoke(prompt)
        composed_document = response.content

        # Clean up the document to remove any potential commentary
        composed_document = self._clean_document(composed_document)

        return composed_document

    def revise_document(self, document: str, revision_instructions: str) -> str:
        """
        Revise a document based on user instructions

        Args:
            document: Current document content
            revision_instructions: Instructions for revision

        Returns:
            Revised document
        """
        prompt = f"""
        I have a legal document that needs to be revised according to specific instructions. Here is the current document:

        {document}

        Please revise the document according to these instructions:

        {revision_instructions}

        IMPORTANT: Return ONLY the complete, revised document without any additional commentary, explanations, or notes. Do not include any text outside the document itself. Do not include any disclaimers, recommendations, or suggestions. Just provide the clean document text that follows the template format.
        """

        # Generate revised document using the LLM
        response = self.llm.invoke(prompt)
        revised_document = response.content

        # Clean up the document to remove any potential commentary
        revised_document = self._clean_document(revised_document)

        return revised_document

    def _format_clauses(self, clauses: List[Dict[str, Any]]) -> str:
        """
        Format clauses for inclusion in the prompt

        Args:
            clauses: List of clause dictionaries

        Returns:
            Formatted clauses string
        """
        formatted_clauses = ""

        for i, clause in enumerate(clauses):
            formatted_clauses += f"Clause {i+1} (from {clause['metadata']['source']}):\n{clause['content']}\n\n"

        return formatted_clauses

    def _clean_document(self, document: str) -> str:
        """
        Clean the document to remove any commentary or notes outside the actual document content

        Args:
            document: Document content that may contain commentary

        Returns:
            Clean document content
        """
        # Check if document is empty or None
        if not document:
            print("Warning: Empty document received in _clean_document")
            return ""

        # Remove markdown code blocks if present
        if document.startswith("```") and document.endswith("```"):
            document = document.strip("`")
        document = document.replace("```", "")

        # Remove common AI response prefixes
        prefixes_to_remove = [
            "Here is the composed document:",
            "Here is the revised document:",
            "Here is the final document:",
            "Here's the document:",
            "Document:",
            "Final Document:",
            "Revised Document:"
        ]

        for prefix in prefixes_to_remove:
            if document.strip().startswith(prefix):
                document = document.replace(prefix, "", 1).strip()

        # Remove any lines that look like commentary (typically start with * or have "Important" or "Note" in them)
        lines = document.split("\n")
        clean_lines = []
        in_document = True

        # Skip initial empty lines
        start_index = 0
        while start_index < len(lines) and not lines[start_index].strip():
            start_index += 1

        for line in lines[start_index:]:
            # Skip lines that look like commentary
            if (line.strip().startswith("*") or
                line.strip().startswith("**") or
                line.strip().startswith("#") or
                "Important" in line or
                "Note:" in line or
                "Considerations" in line or
                "Next Steps" in line or
                "Legal Review" in line or
                "Remember" in line):
                in_document = False
                continue

            # If we see a document-like line after commentary, we're back in the document
            if not in_document and (line.strip().startswith("EMPLOYMENT AGREEMENT") or
                                   line.strip().startswith("This") or
                                   line.strip().startswith("IN WITNESS") or
                                   line.strip().startswith("AGREEMENT") or
                                   line.strip().startswith("CONTRACT")):
                in_document = True

            if in_document:
                clean_lines.append(line)

        cleaned_document = "\n".join(clean_lines).strip()

        # Print debug info
        print(f"Original document length: {len(document)}")
        print(f"Cleaned document length: {len(cleaned_document)}")

        # If the cleaned document is empty but the original wasn't, return the original
        if not cleaned_document.strip() and document.strip():
            print("Warning: Cleaning removed all content, returning original document")
            return document

        return cleaned_document
