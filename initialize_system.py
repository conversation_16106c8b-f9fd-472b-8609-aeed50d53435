#!/usr/bin/env python3
"""
System Initialization Script

This script initializes the hierarchical vectorization system for the legal document generator.
It sets up the master database with proper category-based vector stores and validates the system.
"""

import os
import sys
import logging
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Add the current directory to Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from utils.vectorization_manager import VectorizationManager
from database.master_db_manager import MasterDatabaseManager

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('system_initialization.log')
    ]
)

logger = logging.getLogger(__name__)


def main():
    """Main initialization function"""
    print("🚀 Legal Document Generator - System Initialization")
    print("=" * 60)
    
    try:
        # Initialize the vectorization manager
        print("\n📊 Initializing Vectorization Manager...")
        manager = VectorizationManager()
        
        # Check current system status
        print("\n🔍 Checking current system status...")
        status = manager.get_system_status()
        print(f"Current Status: {status['status']}")
        print(f"Message: {status['message']}")
        
        # Ask user if they want to proceed with initialization
        if status['status'] == 'initialized':
            print("\n⚠️  System appears to already be initialized.")
            response = input("Do you want to force rebuild? (y/N): ").strip().lower()
            force_rebuild = response in ['y', 'yes']
        else:
            print("\n🔧 System needs to be initialized.")
            force_rebuild = False
        
        # Initialize the system
        print(f"\n🏗️  {'Rebuilding' if force_rebuild else 'Initializing'} hierarchical vectorization system...")
        print("This may take several minutes depending on the size of your master database...")
        
        success = manager.initialize_system(force_rebuild=force_rebuild)
        
        if not success:
            print("❌ System initialization failed!")
            return False
        
        print("✅ System initialization completed successfully!")
        
        # Display system statistics
        print("\n📈 System Statistics:")
        status = manager.get_system_status()
        if 'statistics' in status:
            stats = status['statistics']
            print(f"  • Total Categories: {stats['total_categories']}")
            print(f"  • Total Files Processed: {stats['total_files']}")
            print(f"  • Available Categories: {len(status['available_categories'])}")
            
            print("\n📁 Category Breakdown:")
            for category, info in stats['categories'].items():
                print(f"  • {category}: {info['files']} files, {info['subcategories']} subcategories")
        
        # Validate the system
        print("\n🔬 Validating system...")
        validation_results = manager.validate_system()
        print(f"Validation Status: {validation_results['overall_status']}")
        
        if validation_results['issues']:
            print("❌ Issues found:")
            for issue in validation_results['issues']:
                print(f"  • {issue}")
        
        if validation_results['warnings']:
            print("⚠️  Warnings:")
            for warning in validation_results['warnings']:
                print(f"  • {warning}")
        
        if validation_results['overall_status'] == 'passed':
            print("✅ System validation passed!")
        elif validation_results['overall_status'] == 'warning':
            print("⚠️  System validation passed with warnings.")
        else:
            print("❌ System validation failed!")
            return False
        
        # Test template mapping
        print("\n🧪 Testing template mapping...")
        template_mapping = manager.get_template_categories_mapping()
        
        mapped_count = sum(1 for category in template_mapping.values() if category != "unmapped")
        total_count = len(template_mapping)
        
        print(f"Template Mapping: {mapped_count}/{total_count} templates successfully mapped")
        
        if mapped_count < total_count:
            unmapped = [path for path, category in template_mapping.items() if category == "unmapped"]
            print("⚠️  Unmapped templates:")
            for template in unmapped[:5]:  # Show first 5
                print(f"  • {template}")
            if len(unmapped) > 5:
                print(f"  • ... and {len(unmapped) - 5} more")
        
        # Test context retrieval with a sample
        print("\n🔍 Testing context retrieval...")
        test_template = None
        for template_path, category in template_mapping.items():
            if category != "unmapped":
                test_template = template_path
                break
        
        if test_template:
            result = manager.test_context_retrieval(test_template, "legal clause", k=2)
            if result['success']:
                print(f"✅ Context retrieval test passed: {result['results_count']} documents retrieved")
                if result['results']:
                    print("Sample result:")
                    sample = result['results'][0]
                    print(f"  • Source: {sample['filename']}")
                    print(f"  • Category: {sample['category']}")
                    print(f"  • Preview: {sample['content_preview'][:100]}...")
            else:
                print(f"❌ Context retrieval test failed: {result['message']}")
        else:
            print("⚠️  No mapped templates available for context retrieval test")
        
        print("\n🎉 System initialization and validation completed!")
        print("\nNext steps:")
        print("1. Run the main application: python app.py")
        print("2. Select a template and test the new form creation system")
        print("3. Generate a document to test the hierarchical RAG system")
        
        return True
        
    except Exception as e:
        logger.error(f"Initialization failed: {e}")
        print(f"❌ Initialization failed: {e}")
        return False


def quick_status():
    """Quick status check without full initialization"""
    try:
        manager = VectorizationManager()
        status = manager.get_system_status()
        
        print("📊 System Status:")
        print(f"Status: {status['status']}")
        print(f"Message: {status['message']}")
        
        if 'statistics' in status:
            stats = status['statistics']
            print(f"Categories: {stats['total_categories']}")
            print(f"Files: {stats['total_files']}")
        
    except Exception as e:
        print(f"❌ Error checking status: {e}")


if __name__ == "__main__":
    import argparse
    
    parser = argparse.ArgumentParser(description="Initialize the Legal Document Generator system")
    parser.add_argument("--status", action="store_true", help="Show system status only")
    parser.add_argument("--force", action="store_true", help="Force rebuild even if system is initialized")
    
    args = parser.parse_args()
    
    if args.status:
        quick_status()
    else:
        success = main()
        sys.exit(0 if success else 1)
